package service

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
)

type PayNotifyService struct {
	log           *logger.Logger
	tx            driver.ITransaction
	payOrderRepo  repo.PayOrderRepo
	goodsRepo     repo.GoodsVipRepo
	userRepo      repo.UserRepo
	userVipRepo   repo.UserVipRepo
	configService *ConfigService
	userService   *UserService
	guiyinService *GuiyinService
}

func NewPayNotifyService(
	bootStrap *component.BootStrap,
	payOrderRepo repo.PayOrderRepo,
	goodsRepo repo.GoodsVipRepo,
	userRepo repo.UserRepo,
	userVipRepo repo.UserVipRepo,
	userService *UserService,
	guiyinService *GuiyinService,
	configService *ConfigService,
) *PayNotifyService {
	return &PayNotifyService{
		log:           bootStrap.Log,
		tx:            bootStrap.Tx,
		payOrderRepo:  payOrderRepo,
		goodsRepo:     goodsRepo,
		userRepo:      userRepo,
		userVipRepo:   userVipRepo,
		userService:   userService,
		guiyinService: guiyinService,
		configService: configService,
	}
}

// PayNotify 支付回调
func (b *PayNotifyService) PayNotify(req *dto.PayNotifyDto) error {
	b.log.Info("支付回调参数: %+v", req)

	mysqlTx := b.tx.MysqlDbTxBegin()

	// 查询订单信息
	payOrder, err := b.payOrderRepo.GetPayOrderByOrderID(req.OrderId, mysqlTx)
	if err != nil {
		b.log.Error("查询订单失败: %v, orderId: %v", err, req.OrderId)
		mysqlTx.Rollback()
		return fmt.Errorf("查询订单失败: %v, orderId: %v", err, req.OrderId)
	}
	if payOrder == nil || payOrder.ID == 0 {
		b.log.Error("订单信息不存在: %+v, orderId: %v", payOrder, req.OrderId)
		mysqlTx.Rollback()
		return fmt.Errorf("订单信息不存在: %+v, orderId: %v", payOrder, req.OrderId)
	}
	b.log.Info("查询到订单信息: %+v", payOrder)

	// 保存支付回调信息
	callBackJson, err := json.Marshal(req)
	if err != nil {
		b.log.Error("订单[%v]支付回调信息转换JSON失败: %v", payOrder.OrderID, err)
		mysqlTx.Rollback()
		return fmt.Errorf("订单[%v]支付回调信息转换JSON失败: %v", payOrder.OrderID, err)
	}
	payOrder.WnlCallbackData = string(callBackJson)
	b.log.Info("订单[%v]支付回调信息: %s", payOrder.OrderID, payOrder.WnlCallbackData)

	// 转换金额格式
	fee, err := strconv.ParseFloat(req.Fee, 64)
	if err != nil {
		b.log.Error("订单[%v]退款金额[%v]转换格式失败: %v\n", payOrder.OrderID, req.Fee, err)
		mysqlTx.Rollback()
		return fmt.Errorf("订单[%v]退款金额[%v]转换格式失败: %v", payOrder.OrderID, req.Fee, err)
	}

	// 是否是退款
	if req.NotifyType != nil && *req.NotifyType == "REFUND" {
		b.log.Info("订单[%v]退款回调, 退款金额: %v", payOrder.OrderID, fee)

		payOrder.RefundAmount = fee

		if err := b.refund(payOrder, mysqlTx); err != nil {
			b.log.Error("订单[%v]退款失败: %v\n", payOrder.OrderID, err)
			mysqlTx.Rollback()
			return fmt.Errorf("订单[%v]退款失败: %v", payOrder.OrderID, err)
		}

		if err := mysqlTx.Commit().Error; err != nil {
			b.log.Error("订单[%v]退款提交事务失败: %v\n", payOrder.OrderID, err)
			mysqlTx.Rollback()
			return fmt.Errorf("订单[%v]退款提交事务失败: %v", payOrder.OrderID, err)
		}

		b.log.Info("订单[%v]退款成功, 退款金额: %v", payOrder.OrderID, fee)
		return nil
	}

	// 支付成功回调
	payOrder.PayAmount = fee
	if err := b.paySuccess(req, payOrder, mysqlTx); err != nil {
		b.log.Error("订单[%v]支付信息修改失败: %v", payOrder.OrderID, err)
		mysqlTx.Rollback()
		return fmt.Errorf("订单[%v]支付信息修改失败: %v", payOrder.OrderID, err)
	}

	// 提交事务
	if err := mysqlTx.Commit().Error; err != nil {
		b.log.Error("订单[%v]支付提交事务失败: %v\n", payOrder.OrderID, err)
		mysqlTx.Rollback()
		return fmt.Errorf("订单[%v]支付提交事务失败: %v", payOrder.OrderID, err)
	}

	b.log.Info("订单[%v]支付成功, 支付金额: %v", payOrder.OrderID, payOrder.PayAmount)
	return nil
}

func (b *PayNotifyService) paySuccess(req *dto.PayNotifyDto, payOrder *dto.PayOrderDto, tx *gorm.DB) error {
	// 订单 已支付 / 已退款 / 已过期 不做处理
	if payOrder.OrderState == model.OrderStatePaid ||
		payOrder.OrderState == model.OrderStateRefund ||
		payOrder.OrderState == model.OrderStateExpired {
		b.log.Warning("订单[%v] 已支付/已退款/已过期 不做支付处理", payOrder.OrderID)
		return fmt.Errorf("订单 已支付/已退款/已过期 不做支付处理")
	}

	// 支付成功，修改订单信息
	payOrder.WnlOrderID = req.WnlOrderId
	if req.PayType == model.PayTypeApple && req.Key != nil && *req.Key == 1 {
		payOrder.PayType = model.PayTypeSandbox
	} else {
		payOrder.PayType = int(req.PayType)
	}
	payAt := time.Now()
	payOrder.PayAt = &payAt
	payOrder.UpdateAt = payAt

	if err := b.payOrderRepo.UpdatePayOrderByOrderID(payOrder.OrderID, map[string]any{
		"pay_amount":        payOrder.PayAmount,
		"wnl_order_id":      payOrder.WnlOrderID,
		"pay_type":          payOrder.PayType,
		"order_state":       model.OrderStatePaid,
		"pay_at":            payOrder.PayAt,
		"update_at":         payOrder.UpdateAt,
		"wnl_callback_data": payOrder.WnlCallbackData,
	}, tx); err != nil {
		b.log.Error("修改订单失败: %v, orderId: %v", err, payOrder.OrderID)
		return fmt.Errorf("修改订单失败: %v, orderId: %v", err, payOrder.OrderID)
	}

	// 查询商品信息
	vipGoods, err := b.goodsRepo.GetGoods(&dto.VipGoodsInfoDto{Id: int(payOrder.GoodsID)}, tx)
	if err != nil {
		b.log.Error("查询vip商品失败: %v", err.Error())
		return fmt.Errorf("查询vip商品失败: %v", err)
	}
	if vipGoods == nil || vipGoods.Id == 0 {
		b.log.Error("会员商品信息不存在: %+v, goodsId: %v", vipGoods, payOrder.GoodsID)
		return fmt.Errorf("会员商品信息不存在: %+v, goodsId: %v", vipGoods, payOrder.GoodsID)
	}

	// 查询用户信息
	user, err := b.userRepo.GetUserInfoByUserId(payOrder.UserID, tx)
	if err != nil {
		b.log.Error("查询用户信息失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("查询用户信息失败: %v, userId: %v", err, payOrder.UserID)
	}
	if user == nil || user.ID == 0 {
		b.log.Error("用户信息不存在: %+v, userId: %v", user, payOrder.UserID)
		return fmt.Errorf("用户信息不存在: %+v, userId: %v", user, payOrder.UserID)
	}

	var monthlyDiamond = uint64(vipGoods.MonthlyDiamond)
	if vipGoods.MonthlyDiamond == 0 {
		// 获取配置的默认VIP赠送钻石数量
		config, err := b.configService.GetConfigByKeyFromCache(context.Background(), "default_vip_give_diamond", tx)
		if err != nil {
			b.log.Error("获取VIP赠送钻石配置失败: %v", err)
			return fmt.Errorf("获取VIP赠送钻石配置失败: %v", err)
		}
		var defaultVipGiveDiamond dto.DefaultVipGiveDiamondDto
		if err := json.Unmarshal([]byte(config), &defaultVipGiveDiamond); err != nil {
			b.log.Error("解析VIP赠送钻石配置失败: %v", err)
			return fmt.Errorf("解析VIP赠送钻石配置失败: %v", err)
		}

		switch vipGoods.VipType {
		case model.VipTypeMonth:
			monthlyDiamond = defaultVipGiveDiamond.MonthGive
		case model.VipTypeQuarter:
			monthlyDiamond = defaultVipGiveDiamond.QuarterGive
		case model.VipTypeYear:
			monthlyDiamond = defaultVipGiveDiamond.YearGive
		}

		if monthlyDiamond == 0 {
			b.log.Error("开通VIP赠送钻石数量为0, vipGoods: %+v, userId: %v", vipGoods, payOrder.UserID)
			return fmt.Errorf("开通VIP赠送钻石数量为0, vipGoods: %+v, userId: %v", vipGoods, payOrder.UserID)
		}
	}

	// 续费会员
	if user.IsVip != int8(model.NotVip) && user.VipType != int8(model.NotVip) {
		b.log.Info("订单[%v]为用户[%d]续费会员, 会员类型: %d", payOrder.OrderID, user.ID, vipGoods.VipType)

		if err := b.renewVIP(user, payOrder, vipGoods, monthlyDiamond, tx); err != nil {
			b.log.Error("订单[%v]续费会员失败: %v\n", payOrder.OrderID, err)
			return fmt.Errorf("订单[%v]续费会员失败: %v", payOrder.OrderID, err)
		}

		b.log.Info("订单[%v]续费VIP成功", payOrder.OrderID)
		return nil
	}

	// 开通会员
	b.log.Info("订单[%v]为用户[%d]开通会员, 会员类型: %d", payOrder.OrderID, user.ID, vipGoods.VipType)
	if err := b.activateVIP(user, payOrder, vipGoods, monthlyDiamond, tx); err != nil {
		b.log.Error("订单[%v]开通会员失败: %v\n", payOrder.OrderID, err)
		return fmt.Errorf("订单[%v]开通会员失败: %v", payOrder.OrderID, err)
	}

	b.log.Info("订单[%v]开通会员成功", payOrder.OrderID)
	return nil
}

// 开通会员
func (b *PayNotifyService) activateVIP(user *dto.UserInfoDto, payOrder *dto.PayOrderDto, vipGoods *dto.VipGoodsInfoDto, monthlyDiamond uint64, tx *gorm.DB) (err error) {
	b.log.Info("开通会员, 用户ID: %d, 会员类型: %d, 赠送钻石数量: %d", user.ID, vipGoods.VipType, monthlyDiamond)

	if _, err := b.userService.OpenVip(&dto.CreateAndRenewUserVipRequest{
		UserId:  user.ID,
		GoodsID: payOrder.GoodsMiddleID,
		OrderID: payOrder.OrderID,
		VipType: int8(vipGoods.VipType),
		Diamond: monthlyDiamond,
		Version: payOrder.Version,
		Channel: payOrder.Channel,
	}, tx); err != nil {
		b.log.Error("开通会员失败: %v, userId: %v", err, user.ID)
		return fmt.Errorf("开通会员失败: %v, userId: %v", err, user.ID)
	}

	b.log.Info("用户[%d]开通会员成功, 会员类型: %d", user.ID, vipGoods.VipType)

	// 将用户任务队列改为vip队列
	//if err := b.queueDao.UpdateUserAllTaskQueueIsVip(payOrder.UserID, 1, tx); err != nil {
	//	logger.Log().Error("修改用户任务队列为vip队列失败: %v, userId: %v", err, payOrder.UserID)
	//	return fmt.Errorf("修改用户任务队列为vip队列失败: %v, userId: %v", err, payOrder.UserID)
	//}

	return nil
}

// 续费会员
func (b *PayNotifyService) renewVIP(user *dto.UserInfoDto, payOrder *dto.PayOrderDto, vipGoods *dto.VipGoodsInfoDto, monthlyDiamond uint64, tx *gorm.DB) error {
	b.log.Info("续费会员, 用户ID: %d, 会员类型: %d, 赠送钻石数量: %d", user.ID, vipGoods.VipType, monthlyDiamond)

	if err := b.userService.RenewVip(&dto.CreateAndRenewUserVipRequest{
		UserId:  user.ID,
		GoodsID: payOrder.GoodsMiddleID,
		OrderID: payOrder.OrderID,
		VipType: int8(vipGoods.VipType),
		Diamond: monthlyDiamond,
		Version: payOrder.Version,
		Channel: payOrder.Channel,
	}, tx); err != nil {
		b.log.Error("续费会员失败: %v, userId: %v", err, user.ID)
		return fmt.Errorf("续费会员失败: %v, userId: %v", err, user.ID)
	}

	b.log.Info("用户[%d]续费会员成功, 会员类型: %d", user.ID, vipGoods.VipType)

	return nil
}

// 退款
func (b *PayNotifyService) refund(payOrder *dto.PayOrderDto, tx *gorm.DB) (err error) {
	// 是否已经退款 未支付/已退款/已过期 不做处理
	if payOrder.OrderState == model.OrderStatePending ||
		payOrder.OrderState == model.OrderStateRefund ||
		payOrder.OrderState == model.OrderStateExpired {
		b.log.Warning("订单[%v] 未支付/已退款 不做退款处理", payOrder.OrderID)
		return fmt.Errorf("订单 未支付/已退款 不做退款处理")
	}

	// 查询商品信息
	vipGoods, err := b.goodsRepo.GetGoods(&dto.VipGoodsInfoDto{Id: int(payOrder.GoodsID)}, tx)
	if err != nil {
		b.log.Error("查询vip商品失败: %v", err.Error())
		return fmt.Errorf("查询vip商品失败: %v", err)
	}
	if vipGoods == nil || vipGoods.Id == 0 {
		b.log.Error("会员商品信息不存在: %+v, goodsId: %v", vipGoods, payOrder.GoodsID)
		return fmt.Errorf("会员商品信息不存在: %+v, goodsId: %v", vipGoods, payOrder.GoodsID)
	}

	// 会员商品退款
	if err := b.refundVIP(payOrder, vipGoods, tx); err != nil {
		b.log.Error("会员商品退款失败: %v, goodsId: %v", err, payOrder.GoodsID)
		return fmt.Errorf("会员商品退款失败: %v, goodsId: %v", err, payOrder.GoodsID)
	}

	// 修改订单为已退款，并记录退款时间
	refundTime := time.Now()
	if err = b.payOrderRepo.UpdatePayOrderByOrderID(payOrder.OrderID, map[string]any{
		"refund_amount":     payOrder.RefundAmount,
		"order_state":       model.OrderStateRefund,
		"refund_at":         &refundTime,
		"update_at":         &refundTime,
		"wnl_callback_data": payOrder.WnlCallbackData,
	}, tx); err != nil {
		b.log.Error("修改订单失败: %v, orderId: %v", err, payOrder.OrderID)
		return fmt.Errorf("修改订单失败: %v, orderId: %v", err, payOrder.OrderID)
	}
	return nil
}

func (b *PayNotifyService) refundVIP(payOrder *dto.PayOrderDto, vipGoods *dto.VipGoodsInfoDto, tx *gorm.DB) error {
	// 根据 userId 查询用户信息
	user, err := b.userRepo.GetUserInfoByUserId(payOrder.UserID, tx)
	if err != nil {
		b.log.Error("查询用户失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("查询用户失败: %v, userId: %v", err, payOrder.UserID)
	}
	if user == nil || user.ID == 0 {
		b.log.Error("用户信息不存在: %+v, userId: %v", user, payOrder.UserID)
		return fmt.Errorf("用户信息不存在: %+v, userId: %v", user, payOrder.UserID)
	}

	// 查询用户VIP信息
	vipInfo, err := b.userVipRepo.GetUserVipByUserId(payOrder.UserID, tx)
	if err != nil {
		b.log.Error("查询vip信息失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("查询vip信息失败: %v, userId: %v", err, payOrder.UserID)
	}
	if vipInfo == nil || vipInfo.ID == 0 {
		b.log.Error("用户vip信息不存在: %+v, userId: %v", vipInfo, payOrder.UserID)
		return fmt.Errorf("用户vip信息不存在: %+v, userId: %v", vipInfo, payOrder.UserID)
	}

	var monthlyDiamond = uint64(vipGoods.MonthlyDiamond)
	if vipGoods.MonthlyDiamond == 0 {
		// 获取配置的默认VIP赠送钻石数量
		config, err := b.configService.GetConfigByKeyFromCache(context.Background(), "default_vip_give_diamond", tx)
		if err != nil {
			b.log.Error("获取VIP赠送钻石配置失败: %v", err)
			return fmt.Errorf("获取VIP赠送钻石配置失败: %v", err)
		}
		var defaultVipGiveDiamond dto.DefaultVipGiveDiamondDto
		if err := json.Unmarshal([]byte(config), &defaultVipGiveDiamond); err != nil {
			b.log.Error("解析VIP赠送钻石配置失败: %v", err)
			return fmt.Errorf("解析VIP赠送钻石配置失败: %v", err)
		}

		switch vipGoods.VipType {
		case model.VipTypeMonth:
			monthlyDiamond = defaultVipGiveDiamond.MonthGive
		case model.VipTypeQuarter:
			monthlyDiamond = defaultVipGiveDiamond.QuarterGive
		case model.VipTypeYear:
			monthlyDiamond = defaultVipGiveDiamond.YearGive
		}
		if monthlyDiamond == 0 {
			b.log.Error("会员每月赠送钻石数量为0, vipGoods: %+v, userId: %v", vipGoods, payOrder.UserID)
			return fmt.Errorf("会员每月赠送钻石数量为0, vipGoods: %+v, userId: %v", vipGoods, payOrder.UserID)
		}
	}

	if vipInfo.RenewalAt == nil || vipInfo.RenewalAt.IsZero() {
		// 开通VIP退款
		if err := b.refundActivateVIP(user, payOrder, vipGoods, monthlyDiamond, tx); err != nil {
			b.log.Error("开通VIP退款失败: %v, userId: %v", err, payOrder.UserID)
			return fmt.Errorf("开通VIP退款失败: %v, userId: %v", err, payOrder.UserID)
		}
	} else {
		// 续费VIP退款
		if err := b.refundRenewVIP(user, vipGoods, vipInfo, tx); err != nil {
			b.log.Error("续费VIP退款失败: %v, userId: %v", err, payOrder.UserID)
			return fmt.Errorf("续费VIP退款失败: %v, userId: %v", err, payOrder.UserID)
		}
	}
	return nil
}

func (b *PayNotifyService) refundRenewVIP(user *dto.UserInfoDto, vipGoods *dto.VipGoodsInfoDto, vipInfo *dto.UserVipDto, tx *gorm.DB) error {
	// 将到期时间回退到续费之前
	var expireAt time.Time
	switch vipGoods.VipType {
	case model.VipTypeMonth:
		expireAt = vipInfo.ExpireAt.AddDate(0, -1, 0)
	case model.VipTypeQuarter:
		expireAt = vipInfo.ExpireAt.AddDate(0, -6, 0)
	case model.VipTypeYear:
		expireAt = vipInfo.ExpireAt.AddDate(-1, 0, 0)
	}

	// 获取旧会员信息列表，取出旧会员信息的会员类型
	var oldVipInfoList []dto.UserVipDto
	var oldVipType int8
	if vipInfo.OldVipInfo != "" {
		if err := json.Unmarshal([]byte(vipInfo.OldVipInfo), &oldVipInfoList); err != nil {
			b.log.Error("解析旧会员信息失败: %v, userId: %v", err, user.ID)
			return fmt.Errorf("解析旧会员信息失败: %v, userId: %v", err, user.ID)
		}
	}
	if len(oldVipInfoList) == 0 {
		b.log.Error("旧会员信息列表为空, 无法恢复旧会员类型, userId: %v", user.ID)
		return fmt.Errorf("旧会员信息列表为空, 无法恢复旧会员类型, userId: %v", user.ID)
	}
	oldVipType = oldVipInfoList[len(oldVipInfoList)-1].VipType

	var oldVipInfoListJson = ""
	if len(oldVipInfoList) > 1 {
		oldVipInfoList = oldVipInfoList[:len(oldVipInfoList)-1] // 去掉最后一条续费记录
		oldVipInfoListJsonBytes, err := json.Marshal(oldVipInfoList)
		if err != nil {
			b.log.Error("序列化旧会员信息失败: %v, userId: %v", err, user.ID)
			return fmt.Errorf("序列化旧会员信息失败: %v, userId: %v", err, user.ID)
		}
		oldVipInfoListJson = string(oldVipInfoListJsonBytes)
	}

	// 更新用户VIP信息
	if err := b.userVipRepo.UpdateUserVipById(vipInfo.ID, map[string]any{
		"vip_type":     oldVipType, // 恢复到旧会员类型
		"expire_at":    expireAt,
		"renewal_at":   nil,
		"old_vip_info": oldVipInfoListJson,
	}); err != nil {
		b.log.Error("更新用户VIP信息失败: %v, userId: %v", err, user.ID)
		return fmt.Errorf("更新用户VIP信息失败: %v, userId: %v", err, user.ID)
	}

	// 更新用户信息中的会员状态
	if err := b.userService.UpdateUser(&dto.UpdateUserRequest{
		ID:      user.ID,
		VipType: oldVipType,
	}, tx); err != nil {
		b.log.Error("更新用户会员状态失败: %v, userId: %v", err, user.ID)
		return fmt.Errorf("更新用户会员状态失败: %v, userId: %v", err, user.ID)
	}

	// 获取赠送记录条数
	vipGiveCount := b.userService.CalculateGiveRecords(int8(vipGoods.VipType), true)

	// 获取用户开通时创建的几条赠送记录
	vipGiveList, err := b.userVipRepo.GetLatestVipGivesByUserId(user.ID, vipGiveCount, tx)
	if err != nil {
		b.log.Error("查询用户开通时赠送记录失败: %v, userId: %v", err, user.ID)
		return fmt.Errorf("查询用户开通时赠送记录失败: %v, userId: %v", err, user.ID)
	}

	var giveIds = make([]int64, 0, len(vipGiveList))
	for _, v := range vipGiveList {
		giveIds = append(giveIds, v.ID)
	}

	// 删除开通会员时创建的赠送记录
	if err := b.userVipRepo.BatchDeleteVipGiveById(giveIds, tx); err != nil {
		logger.Log().Error("删除用户vip赠送记录失败: %v, userId: %v", err, user.ID)
		return fmt.Errorf("删除用户vip赠送记录失败: %v, userId: %v", err, user.ID)
	}

	return nil
}

// 开通会员退款
func (b *PayNotifyService) refundActivateVIP(
	user *dto.UserInfoDto,
	payOrder *dto.PayOrderDto,
	vipGoods *dto.VipGoodsInfoDto,
	monthlyDiamond uint64,
	tx *gorm.DB,
) error {
	// 扣除当月已赠送的钻石
	user.Diamond -= monthlyDiamond
	if err := b.userService.AddOrSubUserDiamondAndAddRecord(DiamondOperationRequest{
		UserID:  payOrder.UserID,
		OrderID: payOrder.OrderID,
		GoodsID: vipGoods.GoodsId,
		Diamond: monthlyDiamond,
		Mark:    "退款扣除赠送钻石",
		Version: payOrder.Version,
		Channel: payOrder.Channel,
		Op:      int(model.StatusDisabled),
	}, tx); err != nil {
		b.log.Error("扣除用户钻石失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("扣除用户钻石失败: %v, userId: %v", err, payOrder.UserID)
	}

	// 移除用户会员状态
	if err := b.userService.UpdateUser(&dto.UpdateUserRequest{
		ID:      user.ID,
		IsVip:   int8(model.NotVip),
		VipType: int8(model.NotVip),
	}, tx); err != nil {
		b.log.Error("移除用户会员状态失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("移除用户会员状态失败: %v, userId: %v", err, payOrder.UserID)
	}

	// 删除开通会员时保存的会员信息
	if err := b.userVipRepo.UpdateUserVipIsExpireAndIsDelete(user.ID, tx); err != nil {
		b.log.Error("删除用户vip信息失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("删除用户vip信息失败: %v, userId: %v", err, payOrder.UserID)
	}

	// 获取赠送记录条数
	vipGiveCount := b.userService.CalculateGiveRecords(int8(vipGoods.VipType), false)

	// 获取用户开通时创建的几条赠送记录
	vipGiveList, err := b.userVipRepo.GetLatestVipGivesByUserId(user.ID, vipGiveCount, tx)
	if err != nil {
		b.log.Error("查询用户开通时赠送记录失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("查询用户开通时赠送记录失败: %v, userId: %v", err, payOrder.UserID)
	}

	var giveIds = make([]int64, 0, len(vipGiveList))
	for _, v := range vipGiveList {
		giveIds = append(giveIds, v.ID)
	}

	// 删除开通会员时创建的赠送记录
	if err := b.userVipRepo.BatchDeleteVipGiveById(giveIds, tx); err != nil {
		logger.Log().Error("删除用户vip赠送记录失败: %v, userId: %v", err, payOrder.UserID)
		return fmt.Errorf("删除用户vip赠送记录失败: %v, userId: %v", err, payOrder.UserID)
	}

	// 移除所有vip队列信息
	//if err = b.queueDao.UpdateUserAllTaskQueueIsVip(user.ID, 0, tx); err != nil {
	//	logger.Log().Error("移除vip队列信息失败: %v, userId: %v", err, payOrder.UserID)
	//	return fmt.Errorf("移除vip队列信息失败: %v, userId: %v", err, payOrder.UserID)
	//}

	return nil
}
