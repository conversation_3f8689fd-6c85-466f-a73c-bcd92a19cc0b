package controller

import (
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// External API data structures for version controller

// VersionResponse 版本响应数据结构
type VersionResponse struct {
	ID          int64     `json:"id"`
	Version     string    `json:"version"`
	Desc        string    `json:"desc"`
	IsForce     int8      `json:"is_force"`
	Channel     string    `json:"channel"`
	CreateAt    time.Time `json:"create_at"`
	IsDelete    int8      `json:"is_delete"`
	DownloadUrl string    `json:"download_url"`
	VersionInt  int64     `json:"version_int"`
	UpdateAt    time.Time `json:"update_at"`
	IsRelease   int8      `json:"is_release"`
}

// VersionCreateRequest 创建版本外部请求结构
type VersionCreateRequest struct {
	Version     string `json:"version" binding:"required"`
	Desc        string `json:"desc"`
	IsForce     int8   `json:"is_force"`
	Channel     string `json:"channel"`
	DownloadUrl string `json:"download_url"`
	IsRelease   int8   `json:"is_release"`
}

// VersionUpdateRequest 更新版本外部请求结构
type VersionUpdateRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Version     string `json:"version"`
	Desc        string `json:"desc"`
	IsForce     int8   `json:"is_force"`
	Channel     string `json:"channel"`
	DownloadUrl string `json:"download_url"`
	IsRelease   int8   `json:"is_release"`
}

// VersionListRequest 版本列表外部请求结构
type VersionListRequest struct {
	Version     string `json:"version" form:"version"`
	Desc        string `json:"desc" form:"desc"`
	IsForce     int8   `json:"is_force" form:"is_force"`
	Channel     string `json:"channel" form:"channel"`
	DownloadUrl string `json:"download_url" form:"download_url"`
	IsRelease   int8   `json:"is_release" form:"is_release"`
	OrderBy     string `json:"order_by" form:"order_by"`
}

// VersionCountRequest 版本计数外部请求结构
type VersionCountRequest struct {
	Condition map[string]any `json:"condition"`
}

// VersionReleaseRequest 发布版本外部请求结构
type VersionReleaseRequest struct {
	ID int64 `json:"id" form:"id" binding:"required" `
}

// VersionListResponse 版本列表响应结构
type VersionListResponse struct {
	List  []*VersionResponse `json:"list"`
	Total int64              `json:"total"`
}

type VersionController struct {
	versionRepo repo.VersionRepo
}

func NewVersionController(versionRepo repo.VersionRepo) *VersionController {
	return &VersionController{versionRepo: versionRepo}
}

// Conversion functions between external structs and DTOs

// toCreateDTO 将外部创建请求转换为内部DTO
func (req *VersionCreateRequest) toCreateDTO() *dto.VersionDto {
	return &dto.VersionDto{
		Version:     req.Version,
		Desc:        req.Desc,
		IsForce:     model.StatusFlag(req.IsForce),
		Channel:     req.Channel,
		DownloadUrl: req.DownloadUrl,
		IsRelease:   model.StatusFlag(req.IsRelease),
	}
}

// toCountDTO 将外部计数请求转换为内部DTO
func (req *VersionCountRequest) toCountDTO() *dto.VersionQueryRequest {
	queryRequest := &dto.VersionQueryRequest{}

	// 从 condition map 中提取字段
	if req.Condition != nil {
		if id, ok := req.Condition["id"]; ok {
			if idVal, ok := id.(float64); ok {
				queryRequest.ID = int64(idVal)
			}
		}
		if version, ok := req.Condition["version"]; ok {
			if versionVal, ok := version.(string); ok {
				queryRequest.Version = versionVal
			}
		}
		if channel, ok := req.Condition["channel"]; ok {
			if channelVal, ok := channel.(string); ok {
				queryRequest.Channel = channelVal
			}
		}
		if isForce, ok := req.Condition["is_force"]; ok {
			if isForceVal, ok := isForce.(float64); ok {
				queryRequest.IsForce = model.StatusFlag(int8(isForceVal))
			}
		}
		if versionInt, ok := req.Condition["version_int"]; ok {
			if versionIntVal, ok := versionInt.(float64); ok {
				queryRequest.VersionInt = int64(versionIntVal)
			}
		}
		if isDelete, ok := req.Condition["is_delete"]; ok {
			if isDeleteVal, ok := isDelete.(float64); ok {
				queryRequest.IsDelete = model.StatusFlag(int8(isDeleteVal))
			}
		}
		if isRelease, ok := req.Condition["is_release"]; ok {
			if isReleaseVal, ok := isRelease.(float64); ok {
				queryRequest.IsRelease = model.StatusFlag(int8(isReleaseVal))
			}
		}
	}

	return queryRequest
}

// fromDTO 将内部DTO转换为外部响应结构
func fromDTO(dto *dto.VersionDto) *VersionResponse {
	if dto == nil {
		return nil
	}
	return &VersionResponse{
		ID:          dto.ID,
		Version:     dto.Version,
		Desc:        dto.Desc,
		IsForce:     int8(dto.IsForce),
		Channel:     dto.Channel,
		CreateAt:    dto.CreateAt,
		IsDelete:    int8(dto.IsDelete),
		DownloadUrl: dto.DownloadUrl,
		VersionInt:  dto.VersionInt,
		UpdateAt:    dto.UpdateAt,
		IsRelease:   int8(dto.IsRelease),
	}
}

// fromDTOList 将内部DTO列表转换为外部响应结构列表
func fromDTOList(dtos []*dto.VersionDto) []*VersionResponse {
	if dtos == nil {
		return nil
	}
	responses := make([]*VersionResponse, len(dtos))
	for i, d := range dtos {
		responses[i] = fromDTO(d)
	}
	return responses
}

func (a *VersionController) AddVersion(c *gin.Context) {
	var request VersionCreateRequest
	errBind := c.ShouldBind(&request)
	if errBind != nil {
		err := errpkg.NewHighError("参数绑定失败: " + errBind.Error())
		response.Response(c, request, nil, err, response.WithSLSLog)
		return
	}
	dtoRequest := request.toCreateDTO()
	dtoRequest.VersionInt = utils.VersionToVersionInt(request.Version)
	dtoRequest.IsRelease = model.StatusDisabled // 默认不发布
	err := a.versionRepo.Create(dtoRequest)
	var responseErr errpkg.IError
	if err != nil {
		responseErr = errpkg.NewHighError("创建版本失败: " + err.Error())
	}
	response.Response(c, request, nil, responseErr, response.WithSLSLog)
}

func (a *VersionController) UpdateVersion(c *gin.Context) {
	var request VersionUpdateRequest
	errBind := c.ShouldBind(&request)
	if errBind != nil {
		err := errpkg.NewHighError("参数绑定失败: " + errBind.Error())
		response.Response(c, request, nil, err, response.WithSLSLog)
		return
	}

	// 构建更新字段 map
	updates := make(map[string]any)
	if request.Version != "" {
		updates["version"] = request.Version
		updates["version_int"] = utils.VersionToVersionInt(request.Version)
	}
	if request.Desc != "" {
		updates["desc"] = request.Desc
	}
	if request.Channel != "" {
		updates["channel"] = request.Channel
	}
	if request.DownloadUrl != "" {
		updates["download_url"] = request.DownloadUrl
	}
	// 对于 int8 类型，0 也是有效值，所以直接设置

	updates["is_force"] = request.IsForce
	updates["is_release"] = request.IsRelease

	err := a.versionRepo.Update(request.ID, updates)
	var responseErr errpkg.IError
	if err != nil {
		responseErr = errpkg.NewHighError("更新版本失败: " + err.Error())
	}
	response.Response(c, request, nil, responseErr, response.WithSLSLog)
}

func (a *VersionController) GetList(c *gin.Context) {
	var request VersionListRequest
	errBind := c.ShouldBindQuery(&request)
	if errBind != nil {
		err := errpkg.NewHighError("参数绑定失败: " + errBind.Error())
		response.Response(c, request, nil, err, response.WithSLSLog)
		return
	}
	queryRequest := &dto.VersionQueryRequest{

		OrderBy: request.OrderBy,
	}
	queryRequest.Version = request.Version
	queryRequest.Desc = request.Desc
	queryRequest.IsForce = model.StatusFlag(request.IsForce)
	queryRequest.Channel = request.Channel
	queryRequest.DownloadUrl = request.DownloadUrl
	queryRequest.IsRelease = model.StatusFlag(request.IsRelease)

	// 获取列表数据
	list, err := a.versionRepo.List(queryRequest)
	if err != nil {
		responseErr := errpkg.NewHighError("获取版本列表失败: " + err.Error())
		response.Response(c, request, nil, responseErr, response.WithSLSLog)
		return
	}

	count, countErr := a.versionRepo.Count(queryRequest)
	if countErr != nil {
		responseErr := errpkg.NewHighError("获取版本总数失败: " + countErr.Error())
		response.Response(c, request, nil, responseErr, response.WithSLSLog)
		return
	}

	// 构建包含列表和总数的响应数据
	responseData := &VersionListResponse{
		List:  fromDTOList(list),
		Total: count,
	}

	response.Response(c, request, responseData, nil, response.WithSLSLog)
}

func (a *VersionController) GetVersionCount(c *gin.Context) {
	var request VersionCountRequest
	errBind := c.ShouldBind(&request)
	if errBind != nil {
		err := errpkg.NewHighError("参数绑定失败: " + errBind.Error())
		response.Response(c, request, nil, err, response.WithSLSLog)
		return
	}
	count, err := a.versionRepo.Count(request.toCountDTO())
	var responseErr errpkg.IError
	if err != nil {
		responseErr = errpkg.NewHighError("获取版本数量失败: " + err.Error())
	}
	response.Response(c, request, count, responseErr, response.WithSLSLog)
}

func (a *VersionController) GetVersionDetail(c *gin.Context) {
	// 从 URL 参数获取 ID
	idStr := c.Param("id")
	id, parseErr := strconv.ParseInt(idStr, 10, 64)
	if parseErr != nil {
		err := errpkg.NewHighError("ID 参数解析失败: " + parseErr.Error())
		response.Response(c, idStr, nil, err, response.WithSLSLog)
		return
	}

	request := &dto.VersionQueryRequest{}
	request.ID = id
	version, err := a.versionRepo.Select(request)
	var responseErr errpkg.IError
	var responseData *VersionResponse
	if err != nil {
		responseErr = errpkg.NewHighError("获取版本详情失败: " + err.Error())
	} else {
		responseData = fromDTO(version)
	}
	response.Response(c, request, responseData, responseErr, response.WithSLSLog)
}

func (a *VersionController) Upload(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		err := errpkg.NewHighError("获取上传文件失败: " + err.Error())
		response.Response(c, nil, nil, err, response.WithSLSLog)
		return
	}
	// 直接打开文件流
	fileStream, err := file.Open()
	if err != nil {
		err := errpkg.NewHighError("打开文件失败: " + err.Error())
		response.Response(c, nil, nil, err, response.WithSLSLog)
		return
	}
	defer func() {
		_ = fileStream.Close()
	}()
	// TODO: 实现文件上传逻辑
	data := map[string]interface{}{
		"filename": file.Filename,
		"size":     file.Size,
		"message":  "文件上传功能需要实现",
	}
	response.Response(c, nil, data, nil, response.WithSLSLog)
}

func (a *VersionController) ReleaseVersion(c *gin.Context) {
	var request VersionReleaseRequest
	errBind := c.ShouldBindQuery(&request)
	if errBind != nil {
		err := errpkg.NewHighError("参数绑定失败: " + errBind.Error())
		response.Response(c, request, nil, err, response.WithSLSLog)
		return
	}

	// 先查询当前版本的发布状态
	queryRequest := &dto.VersionQueryRequest{}
	queryRequest.ID = request.ID
	currentVersion, err := a.versionRepo.Select(queryRequest)
	if err != nil {
		responseErr := errpkg.NewHighError("查询版本信息失败: " + err.Error())
		response.Response(c, request, nil, responseErr, response.WithSLSLog)
		return
	}

	// 跳跃状态：-1 -> 1, 1 -> -1
	newReleaseStatus := model.StatusFlag(1)
	if currentVersion.IsRelease == model.StatusFlag(1) {
		newReleaseStatus = model.StatusFlag(-1)
	}

	// 更新发布状态
	err = a.versionRepo.Update(request.ID, map[string]any{"is_release": newReleaseStatus})
	var responseErr errpkg.IError
	var message string
	if err != nil {
		responseErr = errpkg.NewHighError("更新版本发布状态失败: " + err.Error())
	} else {
		if newReleaseStatus == model.StatusFlag(1) {
			message = "版本发布成功"
		} else {
			message = "版本取消发布成功"
		}
	}

	responseData := map[string]any{
		"id":         request.ID,
		"is_release": newReleaseStatus,
		"message":    message,
	}
	response.Response(c, request, responseData, responseErr, response.WithSLSLog)
}

func (a *VersionController) DeleteVersion(c *gin.Context) {
	// 从 URL 参数获取 ID
	idStr := c.Param("id")
	id, parseErr := strconv.ParseInt(idStr, 10, 64)
	if parseErr != nil {
		err := errpkg.NewHighError("ID 参数解析失败: " + parseErr.Error())
		response.Response(c, idStr, nil, err, response.WithSLSLog)
		return
	}

	err := a.versionRepo.Update(id, map[string]any{
		"is_delete": model.StatusEnabled, // 设置为已删除状态
	})
	var responseErr errpkg.IError
	if err != nil {
		responseErr = errpkg.NewHighError("删除版本失败: " + err.Error())
	}
	response.Response(c, idStr, nil, responseErr, response.WithSLSLog)
}
