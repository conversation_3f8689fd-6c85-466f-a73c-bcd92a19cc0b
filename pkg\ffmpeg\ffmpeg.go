package ffmpeg

import (
	"fmt"
	"os/exec"
)

func IsFFmpegInstalled() bool {
	_, err := exec.LookPath("ffmpeg")
	return err == nil
}

func ConvertToMP3(inputFile, outputFile string) error {
	if !IsFFmpegInstalled() {
		return fmt.Errorf("FFmpeg is not installed on the system")
	}
	cmd := exec.Command("ffmpeg", "-i", inputFile, outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("FFmpeg error: %v, output: %s", err, string(output))
	}
	return nil
}

// ConvertToAccMP4 转换mp4 acc音频
func ConvertToAccMP4(inputFile, outputFile string) error {
	if !IsFFmpegInstalled() {
		return fmt.Errorf("FFmpeg is not installed on the system")
	}
	cmd := exec.Command("ffmpeg", "-i", inputFile, "-y", "-c:v", "copy", "-c:a", "aac", "-b:a", "128k", outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("FFmpeg error: %v, output: %s", err, string(output))
	}
	return nil
}

// MergeVideoAudio 合并视频和音频文件
func MergeVideoAudio(videoFile, audioFile, outputFile string) error {
	if !IsFFmpegInstalled() {
		return fmt.Errorf("FFmpeg is not installed on the system")
	}
	cmd := exec.Command("ffmpeg", "-i", videoFile, "-i", audioFile, "-c:v", "copy", "-c:a", "aac", "-map", "0:v:0", "-map", "1:a:0", "-shortest", "-y", outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("FFmpeg error: %v, output: %s", err, string(output))
	}
	return nil
}
