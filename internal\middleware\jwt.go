package middleware

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/jwt"
	"chongli/pkg/logger"
	resp "chongli/pkg/response"
	"context"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/panjf2000/ants/v2"
)

type JWTAuthMiddleware struct {
	log           *logger.Logger
	tx            driver.ITransaction
	redisRepo     repo.RedisRepo
	userService   *service.UserService
	configService *service.ConfigService
	antsPool      *ants.Pool
}

func NewJWTAuthMiddleware(
	bootStrap *component.BootStrap,
	redisRepo repo.RedisRepo,
	userService *service.UserService,
	configService *service.ConfigService,
) *JWTAuthMiddleware {
	return &JWTAuthMiddleware{
		log:           bootStrap.Log,
		tx:            bootStrap.Tx,
		redisRepo:     redisRepo,
		userService:   userService,
		configService: configService,
		antsPool:      bootStrap.GoroutinePool,
	}
}

// JWTAuth jwt authentication
func (jm *JWTAuthMiddleware) JWTAuth() gin.HandlerFunc {
	claimKey := "claim"
	return func(c *gin.Context) {
		token := c.Request.Header.Get("X-Token")
		if token == "" {
			err := errpkg.NewMiddleError(resp.PermissionDeniedError)
			resp.Response(c, nil, nil, err, resp.WithSLSLog)
			c.Abort()
			return
		}

		jwtSecret := apollo.GetApolloConfig().JwtSecret
		j := jwt.NewJWT(jwtSecret)
		claims, errParse := j.ParserToken(token)
		if errParse != nil {
			// token过期
			if errParse.Error() == jwt.ValidationErrorExpired {
				err := errpkg.NewMiddleError(resp.TokenExpiredError)
				resp.Response(c, nil, nil, err, resp.WithSLSLog)
				c.Abort()
				return
			}
			err := errpkg.NewMiddleErrorWithCause(errParse, resp.TokenError)
			resp.Response(c, nil, nil, err, resp.WithSLSLog)
			c.Abort()
			return
		}

		// set claims to context.
		jm.setData2Context(c, claimKey, claims)

		// 异步处理每日免费钻石赠送，使用协程池管理
		userId := int(claims.MetaData["user_id"].(float64))
		if userId > 0 {
			jm.submitDiamondTaskAsync(userId)
		}
	}
}

// setData2Context set data to context.
func (jm *JWTAuthMiddleware) setData2Context(c *gin.Context, claimKey string, claims *jwt.CustomClaims) {
	// 将解析后的有效载荷claims重新写入gin.Context引用对象中
	// 将user_id 和 uuid 写进ctx
	meta := claims.MetaData
	if uid, ok := meta["user_id"]; !ok {
		err := errpkg.NewMiddleError(resp.TokenError)
		resp.Response(c, nil, nil, err, resp.WithSLSLog)
		c.Abort()
		return
	} else {
		c.Set("user_id", uid)
	}
	c.Set(claimKey, claims)
}

// submitDiamondTaskAsync 安全地提交钻石赠送任务到协程池
func (jm *JWTAuthMiddleware) submitDiamondTaskAsync(userId int) {
	if userId <= 0 {
		jm.log.Error("无效的用户ID: %d", userId)
		return
	}

	// 使用非阻塞方式提交到协程池，如果池满则直接跳过
	err := jm.antsPool.Submit(func() {
		// 在协程池中执行，确保有完整的错误处理
		ctx := context.Background()
		jm.giveUserEveryDayFreeDiamond(ctx, userId)
	})

	if err != nil {
		// 协程池满时，记录日志但不阻塞主流程
		jm.log.Error("提交任务到协程池出错: %v", err)
		jm.log.Warning("提交任务到协程池失败，跳过用户[%d]的每日钻石赠送处理", userId)
	}
}

func (jm *JWTAuthMiddleware) giveUserEveryDayFreeDiamond(c context.Context, userId int) {
	// 添加完整的 panic 恢复机制
	defer func() {
		if r := recover(); r != nil {
			jm.log.Error("giveUserEveryDayFreeDiamond panic: %v, userId: %d", r, userId)
		}
	}()

	today := time.Now().Format("2006-01-02")
	userIdStr := strconv.Itoa(userId)

	// 先检查Redis缓存，避免重复处理
	isExist, err := jm.redisRepo.Exists(c, "user:"+userIdStr+":"+today)
	if err != nil {
		jm.log.Error("获取 Redis 键失败: %v", err)
		return
	}

	if isExist {
		jm.log.Info("用户[%d]今天已领取过免费钻石", userId)
		return
	}

	// 使用分布式锁防止并发问题
	lockKey := "lock:user_diamond:" + userIdStr + ":" + today
	lockValue := strconv.FormatInt(time.Now().UnixNano(), 10)

	// 尝试获取锁，使用SET NX EX命令确保原子性
	lockAcquired := false
	if err := jm.redisRepo.SetNX(c, lockKey, lockValue, 30*time.Second); err == nil {
		lockAcquired = true
		defer func() {
			// 释放锁，使用Lua脚本确保原子性
			if lockAcquired {
				_ = jm.redisRepo.DelLock(c, lockKey, lockValue)
			}
		}()
	} else {
		jm.log.Warning("获取分布式锁失败，用户[%d]可能正在被其他进程处理: %v", userId, err)
		return
	}

	// 再次检查是否已处理（双重检查）
	isExist, err = jm.redisRepo.Exists(c, "user:"+userIdStr+":"+today)
	if err != nil {
		jm.log.Error("二次检查 Redis 键失败: %v", err)
		return
	}

	if isExist {
		jm.log.Info("用户[%d]今天已领取过免费钻石（二次检查）", userId)
		return
	}

	jm.log.Info("开始赠送用户[%d]今天的免费钻石", userId)

	// 创建数据库事务
	mysqlTx := jm.tx.MysqlDbTxBegin()
	defer func() {
		if mysqlTx != nil {
			mysqlTx.Rollback()
		}
	}()

	// 获取配置
	diamondStr, err := jm.configService.GetConfigByKeyFromCache(c, "every_day_give_diamond")
	if err != nil {
		jm.log.Error("获取配置失败: %v", err)
		return
	}

	diamondNum, err := strconv.Atoi(diamondStr)
	if err != nil {
		jm.log.Error("转换免费钻石数量失败: %v", err)
		return
	}

	// 赠送钻石
	if err := jm.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
		UserID:  int64(userId),
		Diamond: uint64(diamondNum),
		Op:      int(model.StatusEnabled),
		Mark:    "赠送每日免费钻石",
	}, mysqlTx); err != nil {
		jm.log.Error("赠送用户[%d]免费钻石失败: %v", userId, err)
		return
	}

	// 设置 Redis 键，标记今天已赠送过免费钻石
	if err := jm.redisRepo.Set(c, "user:"+userIdStr+":"+today, "1", 24*time.Hour); err != nil {
		jm.log.Error("设置 Redis 键失败: %v", err)
		return
	}

	// 提交事务
	if err := mysqlTx.Commit().Error; err != nil {
		jm.log.Error("事务提交失败: %v", err)
		return
	}

	// 标记事务已提交，避免重复回滚
	mysqlTx = nil

	jm.log.Info("用户[%d]领取免费钻石成功", userId)
}
