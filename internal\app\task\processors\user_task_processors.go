package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"encoding/json"
	"time"
)

type UserTaskProcessors struct {
	bootstrap   *component.BootStrap
	userService *service.UserService
	userRepo    repo.UserRepo
	userVipRepo repo.UserVipRepo
	configRepo  repo.ConfigRepo
}

// NewUserTaskProcessors 创建用户任务处理器实例
func NewUserTaskProcessors(
	bootstrap *component.BootStrap,
	userRepo repo.UserRepo,
	userVipRepo repo.UserVipRepo,
	configRepo repo.ConfigRepo,
	userService *service.UserService,
) *UserTaskProcessors {
	return &UserTaskProcessors{
		bootstrap:   bootstrap,
		userService: userService,
		userRepo:    userRepo,
		userVipRepo: userVipRepo,
		configRepo:  configRepo,
	}
}

// CheckAndUpdateUserVipStatus 检查并更新会员状态
func (s *UserTaskProcessors) CheckAndUpdateUserVipStatus() {
	s.bootstrap.Log.Info("开始检查并更新会员状态")
	mysqlTx := s.bootstrap.Tx.MysqlDbTxBegin()

	// 获取所有今天到期的会员用户
	now := time.Now()
	today := time.Date(
		now.Year(), now.Month(), now.Day(), // 年月日
		0, 0, 0, 0, // 时分秒纳秒设为0
		now.Location(), // 使用当前时区
	)
	userVips, err := s.userVipRepo.ListTodayExpireVIP(today, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("获取所有会员用户失败: %v", err)
		return
	}

	if len(userVips) == 0 {
		mysqlTx.Rollback()
		s.bootstrap.Log.Info("今天没有到期的会员用户")
		return
	}

	// 批量更新用户会员状态
	userIds := make([]int64, 0, len(userVips))
	for _, userVip := range userVips {
		userIds = append(userIds, userVip.UserId)
	}
	if err := s.userRepo.BatchUpdateUserInfoByIds(userIds, map[string]any{
		"is_vip":   model.NotVip,
		"vip_type": model.NotVip,
	}); err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("批量更新用户会员状态失败: %v", err)
		return
	}

	// 批量更新用户会员信息
	if err := s.userVipRepo.BatchUpdateUserVipIsExpireAndIsDelete(userIds, mysqlTx); err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("批量更新用户会员信息失败: %v", err)
		return
	}

	// 提交事务
	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("提交事务失败: %v", err)
		return
	}

	s.bootstrap.Log.Info("成功更新 %d 位会员用户状态", len(userIds))
}

// GiveDiamondForVip 赠送VIP用户钻石
func (s *UserTaskProcessors) GiveDiamondForVip() {
	logger.Log().Info("开始向所有今天需要赠送每月钻石的VIP赠送钻石")
	mysqlTx := s.bootstrap.Tx.MysqlDbTxBegin()

	// 查询所有今天需要赠送钻石的VIP
	now := time.Now()
	today := time.Date(
		now.Year(), now.Month(), now.Day(), // 年月日
		0, 0, 0, 0, // 时分秒纳秒设为0
		now.Location(), // 使用当前时区
	)
	giveVip, err := s.userVipRepo.ListTodayGiveVIP(today, mysqlTx)
	if err != nil {
		s.bootstrap.Log.Error("查询所有今天需要赠送钻石的VIP失败: %v", err)
		mysqlTx.Rollback()
		return
	}
	if len(giveVip) == 0 {
		mysqlTx.Rollback()
		s.bootstrap.Log.Info("今天没有需要赠送每月钻石的VIP")
		return
	}
	s.bootstrap.Log.Info("今天需要赠送每月钻石的VIP数量: %d", len(giveVip))

	// 获取配置的默认VIP赠送钻石数量
	config, err := s.configRepo.GetConfigByKey("default_vip_give_diamond", mysqlTx)
	if err != nil {
		s.bootstrap.Log.Error("获取VIP赠送钻石配置失败: %v", err)
		mysqlTx.Rollback()
		return
	}
	var defaultVipGiveDiamond dto.DefaultVipGiveDiamondDto
	if err := json.Unmarshal([]byte(config.ConfigValue), &defaultVipGiveDiamond); err != nil {
		s.bootstrap.Log.Error("解析VIP赠送钻石配置失败: %v", err)
		mysqlTx.Rollback()
		return
	}

	var monthlyDiamond uint64
	for _, vip := range giveVip {
		// 如果VIP有设置每月赠送钻石数量, 则使用VIP设置的钻石数量
		monthlyDiamond = uint64(vip.GiveNum)
		if monthlyDiamond == 0 {
			switch vip.VipType {
			case model.VipTypeMonth:
				monthlyDiamond = defaultVipGiveDiamond.MonthGive
			case model.VipTypeQuarter:
				monthlyDiamond = defaultVipGiveDiamond.QuarterGive
			case model.VipTypeYear:
				monthlyDiamond = defaultVipGiveDiamond.YearGive
			}
		}

		// 赠送钻石
		if err := s.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
			UserID:  vip.UserId,
			OrderID: vip.OrderId,
			GoodsID: vip.GoodsId,
			Diamond: monthlyDiamond,
			Mark:    "会员每月赠送钻石",
			Op:      int(model.StatusEnabled),
		}, mysqlTx); err != nil {
			s.bootstrap.Log.Error("赠送钻石失败: %v", err.Error())
			mysqlTx.Rollback()
			return
		}
	}

	// 批量更新这些赠送记录的状态为已赠送和已删除
	if err := s.userVipRepo.BatchUpdateVipGiveStatusAndIsDelete(giveVip, mysqlTx); err != nil {
		s.bootstrap.Log.Error("批量更新VIP赠送记录状态失败: %v", err)
		mysqlTx.Rollback()
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		s.bootstrap.Log.Error("提交事务失败: %v", err)
		mysqlTx.Rollback()
		return
	}
	s.bootstrap.Log.Info("已成功向%d位会员用户赠送每月钻石", len(giveVip))
}
