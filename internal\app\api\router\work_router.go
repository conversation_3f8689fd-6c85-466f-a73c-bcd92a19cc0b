package router

// #region 所有和作品相关的路由

import (
	"chongli/internal/app/api/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type TemplateRouter struct {
	templateCategoryCtrl *controller.TemplateCategoryController
	templateCtrl         *controller.TemplateController
	userWorkCtrl         *controller.UserWorksController
	JwtAuthMiddleware    *middleware.JWTAuthMiddleware
}

func NewTemplateRouter(
	engine *gin.Engine,
	templateCategoryCtrl *controller.TemplateCategoryController,
	templateCtrl *controller.TemplateController,
	userWorkCtrl *controller.UserWorksController,
	JwtAuthMiddleware *middleware.JWTAuthMiddleware,
) *TemplateRouter {
	router := &TemplateRouter{
		templateCategoryCtrl: templateCategoryCtrl,
		templateCtrl:         templateCtrl,
		userWorkCtrl:         userWorkCtrl,
	}
	template := engine.Group("api/template")
	router.initTemplateCategory(template)
	router.initTemplate(template)
	work := engine.Group("api/work")
	work.Use(JwtAuthMiddleware.JWTAuth())
	router.initWork(work)
	return router
}

func (r *TemplateRouter) initTemplateCategory(template *gin.RouterGroup) {
	category := template.Group("category")
	// 获取主分类下模板分类列表
	// v1.0.0 只用于写真分类(id=1)
	category.GET("list/:id", r.templateCategoryCtrl.GetTemplateCategoryList)
}

func (r *TemplateRouter) initTemplate(template *gin.RouterGroup) {
	// 获取分类下模板列表
	// v1.0.0 只用于写真分类
	template.GET("list/:id", r.templateCtrl.GetCategoryTemplateList)
	// 获取主分类下模板列表
	// v1.0.0 用于唱歌,跳舞分类
	template.GET("main-class/list/:id", r.templateCtrl.GetMainClassTemplateList)
	// 获取模板详情
	template.GET("detail/:id", r.templateCtrl.GetTemplateDetail)
}

func (r *TemplateRouter) initWork(work *gin.RouterGroup) {
	// 制作图片作品
	work.POST("make_pic_work", r.userWorkCtrl.CreatePicWorks)
	// 制作跳舞作品
	work.POST("make_dance_work", r.userWorkCtrl.CreateDanceWorks)
	// 制作唱歌作品
	work.POST("make_song_work", r.userWorkCtrl.CreateSongWorks)
	// 获取用户作品列表
	work.GET("list", r.userWorkCtrl.GetUserWorksList)
	// 获取用户作品详情
	work.GET("detail", r.userWorkCtrl.GetUserWorkDetail)
	// 删除用户作品
	work.GET("delete", r.userWorkCtrl.DeleteUserWork)
}
