package repo

import (
	"context"
	"time"
)

// RedisRepo Redis 数据仓库接口
type RedisRepo interface {
	// Get 从 Redis 获取值
	Get(ctx context.Context, key string) (string, error)

	// Set 设置值到 Redis
	Set(ctx context.Context, key string, value string, expiration time.Duration) error

	// Del 从 Redis 删除值
	Del(ctx context.Context, key string) error

	// Exists 检查 key 是否存在
	Exists(ctx context.Context, key string) (bool, error)

	// SetNX 设置值到 Redis（仅当key不存在时）
	SetNX(ctx context.Context, key string, value string, expiration time.Duration) error

	// DelLock 安全删除分布式锁（使用Lua脚本确保原子性）
	DelLock(ctx context.Context, key string, value string) error
}
