package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"gorm.io/gorm"
)

type userBindGetuiDao struct {
	db *gorm.DB
}

func NewUserBindGetuiRepo(bootStrap *component.BootStrap) repo.UserBindGetuiRepo {
	return &userBindGetuiDao{db: bootStrap.Driver.GetMysqlDb()}
}

func (d *userBindGetuiDao) GetBindInfoByUserId(userId int64) (*dto.UserBindGetuiInfoDto, error) {
	var cid model.UserBindGetui
	if err := d.db.Model(&model.UserBindGetui{}).Where("user_id = ?", userId).Find(&cid).Error; err != nil {
		logger.Log().Error("获取用户cid异常: %v", err.Error())
		return nil, err
	}
	return &dto.UserBindGetuiInfoDto{
		Id:     cid.Id,
		UserId: cid.UserId,
		Cid:    cid.ClientId,
	}, nil
}

func (d *userBindGetuiDao) BindUserIdAndCid(userId int64, cid string) error {
	if err := d.db.Create(&model.UserBindGetui{
		UserId:   userId,
		ClientId: cid,
	}).Error; err != nil {
		logger.Log().Error("绑定用户cid异常: %v", err.Error())
		return err
	}
	return nil
}

func (d *userBindGetuiDao) UpdateBind(userId int64, cid string) error {
	if err := d.db.Model(&model.UserBindGetui{}).Where("user_id = ?", userId).Update("client_id", cid).Error; err != nil {
		logger.Log().Error("更新用户cid异常: %v", err.Error())
		return err
	}
	return nil
}
