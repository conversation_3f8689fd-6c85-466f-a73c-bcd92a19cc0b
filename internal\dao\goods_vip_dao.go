package dao

import (
	"chongli/pkg/utils"
	"errors"

	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"

	"github.com/uniplaces/carbon"
	"gorm.io/gorm"
)

// goodsVipDao VIP商品DAO实现
type goodsVipDao struct {
	db *gorm.DB
}

// NewGoodsVipDao 创建VIP商品DAO实例
func NewGoodsVipDao(cb *component.BootStrap) repo.GoodsVipRepo {
	return &goodsVipDao{db: cb.Driver.GetMysqlDb()}
}

func (d *goodsVipDao) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// VIP商品操作实现

// CreateGoods 创建商品
func (d *goodsVipDao) CreateGoods(goods *dto.VipGoodsCreateDto, tx ...*gorm.DB) (*dto.VipGoodsInfoDto, error) {
	// 转换DTO为Model
	goodsModel := &model.GoodsVip{
		GoodsId:        goods.GoodsId,
		Title:          goods.Title,
		Price:          goods.Price,
		VipType:        goods.VipType,
		Version:        goods.Version,
		VersionInt:     int(utils.VersionToVersionInt(goods.Version)),
		Channel:        goods.Channel,
		Sort:           goods.Sort,
		MonthlyDiamond: goods.MonthlyDiamond,
		IsDisplay:      int8(goods.IsDisplay),
		IsDelete:       int8(goods.IsDelete),
	}

	// 执行创建
	if err := d.getDb(tx).Create(goodsModel).Error; err != nil {
		logger.Log().Error("创建VIP商品失败: %v, goods: %+v", err, goods)
		return nil, err
	}

	// 转换为DTO返回
	return d.convertModelToDto(goodsModel), nil
}

// buildQuery 构建VIP商品查询条件
func (d *goodsVipDao) buildQuery(goodsQuery *dto.VipGoodsInfoDto, tx ...*gorm.DB) *gorm.DB {
	query := d.getDb(tx).Model(&model.GoodsVip{})

	if goodsQuery.Id > 0 {
		query = query.Where("id = ?", goodsQuery.Id)
	}

	if len(goodsQuery.GoodsId) > 0 {
		query = query.Where("goods_id = ?", goodsQuery.GoodsId)
	}

	if len(goodsQuery.Channel) > 0 {
		query = query.Where("channel = ?", goodsQuery.Channel)
	}
	if len(goodsQuery.Version) > 0 {
		query = query.Where("version = ?", goodsQuery.Version)
	}
	if goodsQuery.IsDelete == -1 || goodsQuery.IsDelete == 1 {
		query = query.Where("is_delete = ?", goodsQuery.IsDelete)
	}
	if goodsQuery.IsDisplay == -1 || goodsQuery.IsDisplay == 1 {
		query = query.Where("is_display = ?", goodsQuery.IsDisplay)
	}

	return query
}

// convertModelToDto 将 GoodsVip Model 转换为 VipGoodsInfoDto
func (d *goodsVipDao) convertModelToDto(goods *model.GoodsVip) *dto.VipGoodsInfoDto {
	if goods == nil {
		return nil
	}
	return &dto.VipGoodsInfoDto{
		Id:             goods.Id,
		GoodsId:        goods.GoodsId,
		Title:          goods.Title,
		Price:          goods.Price,
		VipType:        goods.VipType,
		Version:        goods.Version,
		VersionInt:     goods.VersionInt,
		Channel:        goods.Channel,
		Sort:           goods.Sort,
		MonthlyDiamond: goods.MonthlyDiamond,
		IsDisplay:      model.StatusFlag(goods.IsDisplay),
		CreatedAt:      carbon.NewCarbon(goods.CreateAt).DateTimeString(),
		UpdatedAt:      carbon.NewCarbon(goods.UpdateAt).DateTimeString(),
		IsDelete:       goods.IsDelete,
	}
}

// GetGoods 查询单条goods
func (d *goodsVipDao) GetGoods(goodsQuery *dto.VipGoodsInfoDto, tx ...*gorm.DB) (*dto.VipGoodsInfoDto, error) {
	query := d.buildQuery(goodsQuery, tx...)
	query = query.Order("sort asc")

	goods := &model.GoodsVip{}
	if err := query.First(&goods).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Log().Error("查询vip商品失败: %v", err.Error())
		return nil, err
	}
	return d.convertModelToDto(goods), nil
}

// GetGoodsList 查询多条goods
func (d *goodsVipDao) GetGoodsList(goodsQuery *dto.VipGoodsInfoDto, tx ...*gorm.DB) (goods []*dto.VipGoodsInfoDto, err error) {
	query := d.buildQuery(goodsQuery, tx...)
	if goodsQuery.OrderBy == "" {
		query = query.Order("update_at desc")
	} else {
		query = query.Order(goodsQuery.OrderBy)
	}
	if goodsQuery.Page > 0 && goodsQuery.Size > 0 {
		query = query.Offset((goodsQuery.Page - 1) * goodsQuery.Size).Limit(goodsQuery.Size)
	}
	var goodsList []model.GoodsVip
	if err = query.Find(&goodsList).Error; err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Log().Error("查询vip商品失败: %v", err.Error())
		return nil, err
	}
	goods = make([]*dto.VipGoodsInfoDto, 0, len(goodsList))
	for _, v := range goodsList {
		goods = append(goods, d.convertModelToDto(&v))
	}
	return goods, nil
}

// Delete 删除goods
func (d *goodsVipDao) Delete(goods *dto.VipGoodsInfoDto, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.GoodsVip{}).Where("id = ?", goods.Id).
		Updates(map[string]any{
			"is_delete": 1,
		}).Error; err != nil {
		logger.Log().Error("删除商品失败, err: %v id: %v", err, goods.Id)
		return err
	}
	return nil
}

// Count 获取goods数量
func (d *goodsVipDao) Count(condition *dto.VipGoodsInfoDto, tx ...*gorm.DB) (count int64, err error) {
	query := d.buildQuery(condition, tx...)
	if err = query.
		Count(&count).Error; err != nil {
		logger.Log().Error("查询商品数量失败: %v, condition: %v", err, condition)
		return 0, err
	}
	return count, nil
}

// Update 更新goods
func (d *goodsVipDao) Update(condition, goods map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.GoodsVip{}).Where(condition).
		Updates(goods).Error; err != nil {
		logger.Log().Error("更新商品失败, err: %v goods: %+v", err, goods)
		return err
	}
	return nil
}

// 渠道绑定操作实现

// buildChannelBaseQuery 构建渠道查询的基础查询，包含连表和预加载
func (d *goodsVipDao) buildChannelBaseQuery(tx ...*gorm.DB) *gorm.DB {
	// 构建基本查询条件，使用Preload预加载关联数据
	query := d.getDb(tx).Preload("GoodsModel").Preload("MarketingChannelModel")

	// 添加连接查询以支持关联表的条件筛选
	query = query.Joins("JOIN goods_vip ON goods_vip.id = goods_vip_channel.goods_id")
	query = query.Joins("JOIN channel ON channel.id = goods_vip_channel.channel_id")

	return query
}

// buildChannelWhereConditions 构建渠道查询的WHERE条件
func (d *goodsVipDao) buildChannelWhereConditions(query *gorm.DB, where dto.GoodsVipChannelReq) *gorm.DB {
	// 基础表条件
	if where.ID > 0 {
		query = query.Where("goods_vip_channel.id = ?", where.ID)
	}
	if where.GoodsID > 0 {
		query = query.Where("goods_vip_channel.goods_id = ?", where.GoodsID)
	}
	if where.ChannelID > 0 {
		query = query.Where("goods_vip_channel.channel_id = ?", where.ChannelID)
	}
	if where.GoodsMiddleId != "" {
		query = query.Where("goods_vip.goods_id = ?", where.GoodsMiddleId)
	}
	if where.GoodsChannel != "" {
		query = query.Where("goods_vip.channel = ?", where.GoodsChannel)
	}
	if where.Version != "" {
		query = query.Where("goods_vip_channel.version_int <= ?", utils.VersionToVersionInt(where.Version))
		query = query.Where("goods_vip.version_int <= ?", utils.VersionToVersionInt(where.Version))
	}

	// GoodsModel 相关条件
	if where.IsDisplay == model.StatusEnabled || where.IsDisplay == model.StatusDisabled {
		query = query.Where("goods_vip.is_display = ?", where.IsDisplay)
	}
	if where.GoodsIsDelete == model.StatusEnabled || where.GoodsIsDelete == model.StatusDisabled {
		query = query.Where("goods_vip.is_delete = ?", where.GoodsIsDelete)
	}

	// MarketingChannelModel 相关条件
	if where.ChannelIsDeleted == model.StatusEnabled || where.ChannelIsDeleted == model.StatusDisabled {
		query = query.Where("channel.is_deleted = ?", where.ChannelIsDeleted)
	}

	return query
}

// CreateChannel 创建渠道绑定
func (d *goodsVipDao) CreateChannel(channel *dto.GoodsVipChannelCreateDto, tx ...*gorm.DB) (*dto.GoodsVipChannelDto, error) {
	// 转换DTO为Model
	channelModel := &model.GoodsVipChannel{
		GoodsID:    channel.GoodsID,
		ChannelID:  channel.ChannelID,
		Version:    channel.Version,
		VersionInt: int(utils.VersionToVersionInt(channel.Version)),
	}

	// 执行创建
	if err := d.getDb(tx).Create(channelModel).Error; err != nil {
		logger.Log().Error("创建渠道绑定失败: %v, channel: %+v", err, channel)
		return nil, err
	}

	// 预加载关联数据并返回
	if err := d.getDb(tx).Preload("GoodsModel").Preload("MarketingChannelModel").
		First(channelModel, channelModel.ID).Error; err != nil {
		logger.Log().Error("查询新创建的渠道绑定失败: %v, id: %v", err, channelModel.ID)
		return nil, err
	}

	// 转换为DTO返回
	return d.convertChannelToDto(channelModel), nil
}

// CreateChannelBatch 批量创建渠道绑定
func (d *goodsVipDao) CreateChannelBatch(channels *dto.GoodsVipChannelBatchCreateDto, tx ...*gorm.DB) ([]*dto.GoodsVipChannelDto, error) {
	var channelModels []*model.GoodsVipChannel
	var result []*dto.GoodsVipChannelDto

	// 批量转换DTO为Model
	for _, channelID := range channels.ChannelIDs {
		channelModel := &model.GoodsVipChannel{
			GoodsID:    channels.GoodsID,
			ChannelID:  channelID,
			Version:    channels.Version,
			VersionInt: int(utils.VersionToVersionInt(channels.Version)),
		}
		channelModels = append(channelModels, channelModel)
	}

	// 批量执行创建
	if err := d.getDb(tx).Create(&channelModels).Error; err != nil {
		logger.Log().Error("批量创建渠道绑定失败: %v, channels: %+v", err, channels)
		return nil, err
	}

	// 预加载关联数据并转换为DTO
	for _, channelModel := range channelModels {
		if err := d.getDb(tx).Preload("GoodsModel").Preload("MarketingChannelModel").
			First(channelModel, channelModel.ID).Error; err != nil {
			logger.Log().Error("查询新创建的渠道绑定失败: %v, id: %v", err, channelModel.ID)
			continue
		}
		result = append(result, d.convertChannelToDto(channelModel))
	}

	return result, nil
}

// GetChannelInfo 查询单条渠道绑定
func (d *goodsVipDao) GetChannelInfo(where dto.GoodsVipChannelReq, tx ...*gorm.DB) (goods *dto.GoodsVipChannelDto, err error) {
	var channelRelation model.GoodsVipChannel

	// 构建查询
	query := d.buildChannelBaseQuery(tx...)
	query = d.buildChannelWhereConditions(query, where)

	// 执行查询
	if err = query.First(&channelRelation).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Log().Error("查询单个会员商品渠道失败: %v, condition: %+v", err, where)
		return nil, err
	}

	// 转换为DTO
	return d.convertChannelToDto(&channelRelation), nil
}

func (d *goodsVipDao) ListChannelGoods(req dto.GoodsVipChannelReq, tx ...*gorm.DB) (goods []*dto.GoodsVipChannelDto, err error) {
	var channelRelations []*model.GoodsVipChannel

	// 构建查询
	query := d.buildChannelBaseQuery(tx...)
	query = d.buildChannelWhereConditions(query, req)

	// 排序处理
	if req.OrderBy != "" {
		query = query.Order(req.OrderBy)
	}

	// 分页处理
	if req.Page > 0 && req.Size > 0 {
		offset := (req.Page - 1) * req.Size
		query = query.Offset(offset).Limit(req.Size)
	}

	// 执行查询
	if err = query.Find(&channelRelations).Error; err != nil {
		logger.Log().Error("查询会员商品渠道失败: %v, condition: %+v", err, req)
		return nil, err
	}

	// 转换为DTO列表
	var result []*dto.GoodsVipChannelDto
	for _, relation := range channelRelations {
		result = append(result, d.convertChannelToDto(relation))
	}

	return result, nil
}

// DeleteChannelByIds 删除渠道绑定
func (d *goodsVipDao) DeleteChannelByIds(id []int, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.GoodsVipChannel{}).Where("id IN ?", id).Delete(&model.GoodsVipChannel{}).Error; err != nil {
		logger.Log().Error("删除商品渠道失败, err: %v id: %v", err, id)
		return err
	}
	return nil
}

// CountChannelInfo 获取渠道绑定数量
func (d *goodsVipDao) CountChannelInfo(where dto.GoodsVipChannelReq, tx ...*gorm.DB) (count int64, err error) {
	// 构建基础查询（计数查询不需要Preload）
	query := d.getDb(tx).Model(&model.GoodsVipChannel{})

	// 添加连接查询以支持关联表的条件筛选
	query = query.Joins("JOIN goods_vip ON goods_vip.id = goods_vip_channel.goods_id")
	query = query.Joins("JOIN channel ON channel.id = goods_vip_channel.channel_id")

	// 构建查询条件
	query = d.buildChannelWhereConditions(query, where)

	// 执行计数查询
	if err = query.Count(&count).Error; err != nil {
		logger.Log().Error("查询渠道绑定数量失败: %v, condition: %+v", err, where)
		return 0, err
	}
	return count, nil
}

// UpdateChannel 更新渠道绑定
func (d *goodsVipDao) UpdateChannel(condition, goods map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.GoodsVipChannel{}).Where(condition).
		Updates(goods).Error; err != nil {
		logger.Log().Error("更新商品渠道失败, err: %v goods: %+v", err, goods)
		return err
	}
	return nil
}

// convertChannelToDto 将模型转换为渠道DTO
func (d *goodsVipDao) convertChannelToDto(relation *model.GoodsVipChannel) *dto.GoodsVipChannelDto {
	if relation == nil {
		return nil
	}

	result := &dto.GoodsVipChannelDto{
		ID:         relation.ID,
		GoodsID:    relation.GoodsID,
		ChannelID:  relation.ChannelID,
		CreateAt:   relation.CreateAt,
		IsDelete:   relation.IsDelete,
		Version:    relation.Version,
		VersionInt: relation.VersionInt,
		Sort:       relation.Sort,
	}

	// 如果预加载了商品信息，则转换商品DTO
	if relation.GoodsModel.Id > 0 {
		result.VipGoodsInfoDto = dto.VipGoodsInfoDto{
			Id:             relation.GoodsModel.Id,
			GoodsId:        relation.GoodsModel.GoodsId,
			Title:          relation.GoodsModel.Title,
			Price:          relation.GoodsModel.Price,
			VipType:        relation.GoodsModel.VipType,
			Version:        relation.GoodsModel.Version,
			VersionInt:     relation.GoodsModel.VersionInt,
			Channel:        relation.GoodsModel.Channel,
			Sort:           relation.GoodsModel.Sort,
			MonthlyDiamond: relation.GoodsModel.MonthlyDiamond,
			IsDisplay:      model.StatusFlag(relation.GoodsModel.IsDisplay),
			IsDelete:       relation.GoodsModel.IsDelete,
			CreatedAt:      carbon.NewCarbon(relation.GoodsModel.CreateAt).DateTimeString(),
			UpdatedAt:      carbon.NewCarbon(relation.GoodsModel.UpdateAt).DateTimeString(),
		}
	}

	// 如果预加载了渠道信息，则设置渠道信息
	if relation.MarketingChannelModel.ID > 0 {
		// 直接使用关联的营销渠道信息
		result.MarketingChannel = dto.MarketingChannelDto{
			ID:        relation.MarketingChannelModel.ID,
			Title:     relation.MarketingChannelModel.Title,
			Type:      relation.MarketingChannelModel.Type,
			BindKey:   relation.MarketingChannelModel.BindKey,
			BindValue: relation.MarketingChannelModel.BindValue,
			IsDeleted: relation.MarketingChannelModel.IsDeleted,
			CreateAt:  relation.MarketingChannelModel.CreateAt,
			UpdateAt:  relation.MarketingChannelModel.UpdateAt,
		}
	}

	return result
}
