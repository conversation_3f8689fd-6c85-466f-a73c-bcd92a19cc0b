package service

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/getui"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type UserService struct {
	log               *logger.Logger
	tx                driver.ITransaction
	configService     *ConfigService
	userRepo          repo.UserRepo
	recordRepo        repo.UserDiamondRecordRepo
	userVipRepo       repo.UserVipRepo
	userBindGetuiRepo repo.UserBindGetuiRepo
}

func NewUserService(
	bootStrap *component.BootStrap,
	configService *ConfigService,
	userRepo repo.UserRepo,
	recordRepo repo.UserDiamondRecordRepo,
	userVipRepo repo.UserVipRepo,
	userBindGetuiRepo repo.UserBindGetuiRepo,
) *UserService {
	return &UserService{
		log:               bootStrap.Log,
		tx:                bootStrap.Tx,
		configService:     configService,
		userRepo:          userRepo,
		recordRepo:        recordRepo,
		userVipRepo:       userVipRepo,
		userBindGetuiRepo: userBindGetuiRepo,
	}
}

func (s *UserService) UpdateUser(req *dto.UpdateUserRequest, tx *gorm.DB) errpkg.IError {
	// 检查用户是否存在
	existingUser, err := s.userRepo.GetUserInfoByUserId(req.ID, tx)
	if err != nil {
		s.log.Error("获取用户信息失败: %v", err)
		return errpkg.NewHighError(response.DbError)
	}

	if existingUser.ID == 0 {
		return errpkg.NewLowError("用户不存在")
	}

	// 更新用户信息
	var updates = make(map[string]any)
	if req.Avatar != "" {
		updates["avatar"] = req.Avatar
	}
	if req.Username != "" {
		updates["username"] = req.Username
	}
	if req.Phone != "" {
		updates["phone"] = req.Phone
	}
	if req.IpLocation != "" {
		updates["ip_location"] = req.IpLocation
	}
	if req.IsVip != 0 {
		updates["is_vip"] = req.IsVip
	}
	if req.VipType != 0 {
		updates["vip_type"] = req.VipType
	}
	if req.IsDelete != 0 {
		updates["is_delete"] = req.IsDelete
	}
	if err := s.userRepo.UpdateUserInfoByUserId(existingUser.ID, updates, tx); err != nil {
		s.log.Error("更新用户信息失败: %v", err)
		return errpkg.NewHighError(response.DbError)
	}
	return nil
}

// DiamondOperationRequest 钻石操作请求结构体
type DiamondOperationRequest struct {
	UserID     int64  `json:"user_id"`     // 用户ID
	OrderID    string `json:"order_id"`    // 订单ID
	GoodsID    string `json:"goods_id"`    // 商品ID
	TemplateID int64  `json:"template_id"` // 模板ID
	Diamond    uint64 `json:"diamond"`     // 钻石数量
	Mark       string `json:"mark"`        // 操作备注
	Version    string `json:"version"`     // 客户端版本
	Channel    string `json:"channel"`     // 渠道
	Op         int    `json:"op"`          // 操作类型：1增加，-1减少
}

func (s *UserService) AddOrSubUserDiamondAndAddRecord(req DiamondOperationRequest, tx *gorm.DB) errpkg.IError {
	// 验证操作类型参数
	if req.Op != int(model.StatusEnabled) && req.Op != int(model.StatusDisabled) {
		s.log.Error("无效的操作类型: %d, 只支持 1(增加) 或 -1(扣减)", req.Op)
		return errpkg.NewLowError("无效的操作类型")
	}

	if tx == nil {
		return errpkg.NewLowError("没有传入数据库事务参数")
	}

	var dbTx = tx

	// 获取用户信息
	user, err := s.userRepo.GetUserInfoByUserId(req.UserID, dbTx)
	if err != nil {
		s.log.Error("获取用户信息失败: %v", err)
		return errpkg.NewHighError(response.DbError)
	}

	var newBalance uint64
	var recordType int

	if req.Op == int(model.StatusEnabled) {
		// 增加钻石
		newBalance = user.Diamond + req.Diamond
		recordType = int(model.StatusEnabled)
	} else {
		// 扣减钻石，检查余额是否足够
		if user.Diamond < req.Diamond {
			s.log.Error("用户 %d 钻石余额不足，当前余额: %d, 需要扣减: %d", req.UserID, user.Diamond, req.Diamond)
			return errpkg.NewLowError("钻石余额不足")
		}
		newBalance = user.Diamond - req.Diamond
		recordType = int(model.StatusDisabled)
	}

	// 更新用户钻石余额
	err = s.userRepo.UpdateUserInfoByUserId(req.UserID, map[string]interface{}{
		"diamond": newBalance,
	}, dbTx)
	if err != nil {
		s.log.Error("更新用户钻石余额失败: %v", err)
		return errpkg.NewHighError(response.DbError)
	}

	// 添加钻石变动记录
	record := &dto.UserDiamondRecordDto{
		UserId:     req.UserID,
		OrderId:    req.OrderID,
		GoodsId:    req.GoodsID,
		TemplateId: req.TemplateID,
		Diamond:    req.Diamond,
		Balance:    newBalance,
		Type:       recordType,
		Mark:       req.Mark,
		Version:    req.Version,
		Channel:    req.Channel,
	}
	if err := s.recordRepo.CreateUserDiamondRecord(record, dbTx); err != nil {
		s.log.Error("创建用户钻石记录失败: %v", err)
		return errpkg.NewHighError(response.DbError)
	}

	return nil
}

// CalculateGiveRecords 计算赠送记录数量
func (s *UserService) CalculateGiveRecords(vipType int8, isRenewal bool) int {
	switch vipType {
	case model.VipTypeMonth:
		if isRenewal {
			return 1 // 续费月卡：1条
		}
		return 0 // 开通月卡：0条（当月已直接赠送）
	case model.VipTypeQuarter:
		if isRenewal {
			return 6 // 续费季卡：6条
		}
		return 5 // 开通季卡：5条（当月已直接赠送，剩余5个月）
	case model.VipTypeYear:
		if isRenewal {
			return 12 // 续费年卡：12条
		}
		return 11 // 开通年卡：11条（当月已直接赠送，剩余11个月）
	default:
		return 0
	}
}

// calculateGiveTime 计算赠送时间
// 每30天赠送一次，避免月份天数差异的问题
func (s *UserService) calculateGiveTime(baseTime time.Time, monthOffset int) time.Time {
	// 每30天赠送一次钻石
	daysToAdd := monthOffset * 30
	return baseTime.AddDate(0, 0, daysToAdd)
}

// OpenVip 开通VIP
func (s *UserService) OpenVip(req *dto.CreateAndRenewUserVipRequest, tx *gorm.DB) (*dto.UserVipDto, error) {
	if req.UserId <= 0 {
		return nil, errors.New("用户ID无效")
	}

	// 验证VIP类型
	if req.VipType < 1 || req.VipType > 3 {
		return nil, errors.New("VIP类型无效")
	}

	if req.Diamond <= 0 {
		return nil, errors.New("赠送钻石数量必须大于0")
	}

	if tx == nil {
		return nil, errors.New("没有传入数据库事务参数")
	}

	// 检查用户是否存在
	user, err := s.userRepo.GetUserInfoByUserId(req.UserId, tx)
	if err != nil {
		s.log.Error("查询用户信息失败: %v", err)
		return nil, err
	}
	if user == nil || user.ID == 0 {
		return nil, errors.New("用户不存在")
	}
	if user.IsDelete == 1 {
		return nil, errors.New("用户已注销")
	}

	// 检查是否已有VIP
	existingVip, err := s.userVipRepo.GetUserVipByUserId(req.UserId, tx)
	if err != nil {
		s.log.Error("查询用户现有VIP失败: %v", err)
		return nil, err
	}
	if existingVip != nil && existingVip.ID != 0 {
		return nil, errors.New("用户已是VIP会员")
	}

	// 开通VIP
	vipInfo, err := s.userVipRepo.CreateUserVip(req, tx)
	if err != nil {
		s.log.Error("开通VIP失败: %v", err)
		return nil, err
	}

	// 更新用户信息中的会员状态
	if err := s.userRepo.UpdateUserInfoByUserId(req.UserId,
		map[string]any{"is_vip": model.IsVip, "vip_type": req.VipType}, tx); err != nil {
		s.log.Error("更新用户会员状态失败: %v", err)
		return nil, err
	}

	// 立即赠送当月钻石
	err = s.AddOrSubUserDiamondAndAddRecord(DiamondOperationRequest{
		UserID:  req.UserId,
		OrderID: req.OrderID,
		GoodsID: req.GoodsID,
		Diamond: req.Diamond,
		Mark:    fmt.Sprintf("购买%s当月赠送", s.getVipTypeName(req.VipType)),
		Version: req.Version,
		Channel: req.Channel,
		Op:      int(model.StatusEnabled),
	}, tx)
	if err != nil {
		s.log.Error("赠送当月钻石失败: %v", err)
		return nil, err
	}

	// 季卡和年卡还需要创建后续月份的赠送记录
	if req.VipType != model.VipTypeMonth {
		err = s.createVipGiveRecords(&CreateVipGiveDto{
			UserId:    req.UserId,
			OrderId:   req.OrderID,
			GoodsId:   req.GoodsID,
			GiveNum:   req.Diamond,
			VipType:   req.VipType,
			BaseTime:  vipInfo.CreateAt,
			IsRenewal: false,
		}, tx)
		if err != nil {
			s.log.Error("创建赠送记录失败: %v", err)
			return nil, err
		}
	}

	s.log.Info("用户 %d 成功开通VIP，类型: %d，赠送钻石: %d", req.UserId, req.VipType, req.Diamond)
	return vipInfo, nil
}

// RenewVip 续费VIP
func (s *UserService) RenewVip(req *dto.CreateAndRenewUserVipRequest, tx *gorm.DB) error {
	if req.UserId <= 0 {
		return errors.New("用户ID无效")
	}

	// 验证VIP类型
	if req.VipType < 1 || req.VipType > 3 {
		return errors.New("VIP类型无效")
	}

	if req.Diamond <= 0 {
		return errors.New("赠送钻石数量必须大于0")
	}

	if tx == nil {
		return errors.New("没有传入数据库事务参数")
	}

	// 检查用户是否存在
	user, err := s.userRepo.GetUserInfoByUserId(req.UserId, tx)
	if err != nil {
		s.log.Error("查询用户信息失败: %v", err)
		return err
	}
	if user == nil || user.ID == 0 {
		return errors.New("用户不存在")
	}
	if user.IsDelete == 1 {
		return errors.New("用户已注销")
	}

	// 续费VIP
	err = s.userVipRepo.RenewUserVip(req, tx)
	if err != nil {
		s.log.Error("续费VIP失败: %v", err)
		return err
	}

	// 更新用户信息中的会员状态
	newVipType := req.VipType
	if user.VipType > newVipType {
		newVipType = user.VipType
	}
	if err := s.userRepo.UpdateUserInfoByUserId(req.UserId,
		map[string]any{"vip_type": newVipType}, tx); err != nil {
		s.log.Error("更新用户会员状态失败: %v", err)
		return err
	}

	// 创建赠送记录（续费不直接赠送钻石）
	// 获取用户最后一条赠送记录，以确定续费赠送记录的起始时间
	lastGive, err := s.userVipRepo.GetLastVipGiveByUserId(req.UserId, tx)
	if err != nil {
		s.log.Error("查询用户最后一条赠送记录失败: %v", err)
		return err
	}

	var baseTime time.Time
	if lastGive != nil && lastGive.IsGive == model.GiveStatusPending {
		// 如果有未赠送的记录，从最后一条记录的赠送时间开始计算
		baseTime = lastGive.GiveAt
		s.log.Info("用户 %d 续费VIP，基于最后未赠送记录时间: %s", req.UserId, baseTime.Format("2006-01-02 15:04:05"))
	} else {
		// 如果没有赠送记录或最后一条记录已赠送，使用续费当天
		baseTime = time.Now()
		if lastGive != nil && lastGive.IsGive == model.GiveStatusDone {
			s.log.Info("用户 %d 续费VIP，最后赠送记录已消费，使用续费当天: %s", req.UserId, baseTime.Format("2006-01-02 15:04:05"))
		} else {
			s.log.Info("用户 %d 续费VIP，没有历史赠送记录，使用续费当天: %s", req.UserId, baseTime.Format("2006-01-02 15:04:05"))
		}
	}

	err = s.createVipGiveRecords(&CreateVipGiveDto{
		UserId:    req.UserId,
		OrderId:   req.OrderID,
		GoodsId:   req.GoodsID,
		GiveNum:   req.Diamond,
		VipType:   req.VipType,
		BaseTime:  baseTime,
		IsRenewal: true,
	}, tx)
	if err != nil {
		s.log.Error("创建续费赠送记录失败: %v", err)
		return err
	}

	s.log.Info("用户 %d 成功续费VIP，类型: %d，创建赠送记录: %d钻石", req.UserId, req.VipType, req.Diamond)
	return nil
}

type CreateVipGiveDto struct {
	UserId    int64     `json:"user_id"`  // 用户ID
	OrderId   string    `json:"order_id"` // 来源订单ID
	GoodsId   string    `json:"goods_id"` // 来源商品ID
	GiveNum   uint64    `json:"give_num"`
	VipType   int8      `json:"vip_type"`
	BaseTime  time.Time `json:"base_time"`  // 赠送记录的基准时间
	IsRenewal bool      `json:"is_renewal"` // 是否为续费
}

// createVipGiveRecords 创建VIP赠送记录
func (s *UserService) createVipGiveRecords(req *CreateVipGiveDto, tx *gorm.DB) error {
	recordCount := s.CalculateGiveRecords(req.VipType, req.IsRenewal)
	if recordCount <= 0 {
		return nil
	}

	var giveList = make([]*dto.UserVipGiveDto, 0, recordCount)
	for i := 0; i < recordCount; i++ {
		// 计算赠送时间（每月一次）
		giveTime := s.calculateGiveTime(req.BaseTime, i+1)

		// 创建赠送记录
		giveList = append(giveList, &dto.UserVipGiveDto{
			UserId:  req.UserId,
			GoodsId: req.GoodsId,
			OrderId: req.OrderId,
			VipType: req.VipType,
			GiveNum: int64(req.GiveNum),
			GiveAt:  giveTime,
		})
	}

	if err := s.userVipRepo.BatchCreateVipGive(giveList, tx); err != nil {
		s.log.Error("批量创建VIP赠送记录失败: %v", err)
		return err
	}

	return nil
}

// getVipTypeName 获取VIP类型名称
func (s *UserService) getVipTypeName(vipType int8) string {
	switch vipType {
	case model.VipTypeMonth:
		return "月卡"
	case model.VipTypeQuarter:
		return "季卡"
	case model.VipTypeYear:
		return "年卡"
	default:
		return "未知"
	}
}

func (s *UserService) GetGetuiCidByUserId(userId uint64) string {
	user, err := s.userBindGetuiRepo.GetBindInfoByUserId(int64(userId))
	if err != nil {
		s.log.Error("获取用户信息失败: %v", err)
		return ""
	}
	return user.Cid
}

func (s *UserService) PushGetuiByUserId(userId uint64, msg getui.Transmission) error {
	cid := s.GetGetuiCidByUserId(userId)
	if cid == "" {
		return nil
	}
	// 发送推送
	_, err := getui.PushMsgByCid(utils.CreateUUid(), cid, &msg, -1)
	if err != nil {
		return err
	}
	return nil
}
