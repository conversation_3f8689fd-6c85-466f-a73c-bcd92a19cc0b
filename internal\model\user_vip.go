package model

import "time"

// UserVip 用户VIP信息
type UserVip struct {
	ID         int64      `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                       // 主键id
	UserId     int64      `gorm:"column:user_id;NOT NULL" json:"user_id"`                               // 用户id
	VipType    int8       `gorm:"column:vip_type;NOT NULL" json:"vip_type"`                             // 会员类型：1-月卡；2-季卡；3-年卡
	CreateAt   time.Time  `gorm:"column:create_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"` // 开通时间
	RenewalAt  *time.Time `gorm:"column:renewal_at" json:"renewal_at"`                                  // 续费时间
	ExpireAt   time.Time  `gorm:"column:expire_at;NOT NULL" json:"expire_at"`                           // 到期时间
	IsExpire   int8       `gorm:"column:is_expire;default:-1;NOT NULL" json:"is_expire"`                // 是否已过期
	IsDelete   int8       `gorm:"column:is_delete;default:-1;NOT NULL" json:"is_delete"`                // 是否删除：-1-未删除；1-已删除
	OldVipInfo string     `gorm:"column:old_vip_info" json:"old_vip_info"`                              // 旧会员信息，续费时使用
}

// TableName 表名称
func (*UserVip) TableName() string {
	return "user_vip"
}

// UserVipGive 会员赠送信息
type UserVipGive struct {
	ID       int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                       // 主键id
	UserId   int64     `gorm:"column:user_id;NOT NULL" json:"user_id"`                               // 用户id
	GoodsId  string    `gorm:"column:goods_id;default:'';NOT NULL" json:"goods_id"`                  // 来源商品ID
	OrderId  string    `gorm:"column:order_id;default:'';NOT NULL" json:"order_id"`                  // 来源订单ID
	VipType  int8      `gorm:"column:vip_type;NOT NULL" json:"vip_type"`                             // 会员类型：1-月卡；2-季卡；3-年卡
	GiveAt   time.Time `gorm:"column:give_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"give_at"`     // 赠送时间
	GiveNum  int64     `gorm:"column:give_num;default:0;NOT NULL" json:"give_num"`                   // 赠送钻石数量
	IsGive   int8      `gorm:"column:is_give;default:-1;NOT NULL" json:"is_give"`                    // 是否已赠送：-1-未赠送；1-已赠送
	IsDelete int8      `gorm:"column:is_delete;default:-1;NOT NULL" json:"is_delete"`                // 是否删除：-1-未删除；1-已删除
	CreateAt time.Time `gorm:"column:create_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"` // 创建时间
}

// TableName 表名称
func (*UserVipGive) TableName() string {
	return "user_vip_give"
}

// VIP类型常量
const (
	VipTypeMonth   = 1 // 月卡
	VipTypeQuarter = 2 // 季卡
	VipTypeYear    = 3 // 年卡
)

// 赠送状态常量
const (
	GiveStatusPending = -1 // 未赠送
	GiveStatusDone    = 1  // 已赠送
)

// 过期状态常量
const (
	IsExpire  = 1  // 已过期
	NotExpire = -1 // 未过期
)
