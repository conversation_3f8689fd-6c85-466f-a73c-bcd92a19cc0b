package biz

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
)

type BannerBiz struct {
	log        *logger.Logger
	bannerRepo repo.BannerRepo
}

func NewBannerBiz(
	bootStrap *component.BootStrap,
	bannerRepo repo.BannerRepo,
) *BannerBiz {
	return &BannerBiz{
		log:        bootStrap.Log,
		bannerRepo: bannerRepo,
	}
}

// GetIndexBannerList 获取首页banner列表
func (s *BannerBiz) GetIndexBannerList(version string) ([]*dto.BannerDto, errpkg.IError) {
	// 调用数据层获取banner列表，只查询启用且未删除的banner
	banners, err := s.bannerRepo.ListBanner(version)
	if err != nil {
		s.log.Error("获取首页banner列表失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}

	return banners, nil
}
