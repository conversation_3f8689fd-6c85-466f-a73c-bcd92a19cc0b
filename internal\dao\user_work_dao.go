package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"time"

	"gorm.io/gorm"
)

type userWorkRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewUserWorkRepo(bootStrap *component.BootStrap) repo.UserWorkRepo {
	return &userWorkRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *userWorkRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

func (d *userWorkRepo) Create(_ context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error {
	if userWork.IsDeleted != model.StatusDisabled && userWork.IsDeleted != model.StatusEnabled {
		userWork.IsDeleted = model.StatusDisabled
	}
	data := model.UserWorks{
		UserID:             userWork.UserID,
		TemplateID:         userWork.TemplateID,
		WorkType:           userWork.WorkType,
		Cover:              userWork.Cover,
		Status:             int8(userWork.Status),
		ErrMsg:             userWork.ErrMsg,
		PicURL:             userWork.PicURL,
		VideoURL:           userWork.VideoURL,
		Diamond:            userWork.Diamond,
		IsDeleted:          int8(userWork.IsDeleted),
		ExpectedFinishTime: userWork.ExpectedFinishTime,
		CreateAt:           time.Now(),
		UpdatedAt:          time.Now(),
	}

	err := d.getDb(tx).Create(&data).Error
	if err != nil {
		return err
	}
	// 将生成的ID复制回DTO
	userWork.ID = data.ID
	return nil
}

func (d *userWorkRepo) buildUserWorkQuery(db *gorm.DB, userWork *dto.UserWorks) *gorm.DB {
	query := db.Model(&model.UserWorks{})
	if userWork.ID != 0 {
		query = query.Where("id = ?", userWork.ID)
	}
	if userWork.UserID != 0 {
		query = query.Where("user_id = ?", userWork.UserID)
	}
	if userWork.WorkType != "" {
		query = query.Where("work_type = ?", userWork.WorkType)
	}
	if userWork.TemplateID != 0 {
		query = query.Where("template_id = ?", userWork.TemplateID)
	}
	if userWork.Status != 0 {
		query = query.Where("status = ?", userWork.Status)
	}
	if userWork.PicURL != "" {
		query = query.Where("pic_url = ?", userWork.PicURL)
	}
	if userWork.VideoURL != "" {
		query = query.Where("video_url = ?", userWork.VideoURL)
	}
	if userWork.IsDeleted == model.StatusDisabled || userWork.IsDeleted == model.StatusEnabled {

		query = query.Where("is_deleted = ?", userWork.IsDeleted)
	}
	return query.Preload("User")
}

func (d *userWorkRepo) List(_ context.Context, userWork *dto.UserWorks, withTemplate bool, tx ...*gorm.DB) ([]*dto.UserWorks, error) {
	var modelWorks []model.UserWorks
	query := d.buildUserWorkQuery(d.getDb(tx), userWork).Preload("User")
	query = query.Order("id DESC")
	if withTemplate {
		query = query.Preload("Template")
	}
	// 如果有分页参数，则进行分页查询
	if userWork.Page > 0 && userWork.PageSize > 0 {
		// 计算偏移量
		offset := (userWork.Page - 1) * userWork.PageSize
		// 执行分页查询，按创建时间倒序
		err := query.Order("created_at DESC").Offset(offset).Limit(userWork.PageSize).Find(&modelWorks).Error
		if err != nil {
			return nil, err
		}
	} else {
		// 不分页查询（原逻辑）
		err := query.Find(&modelWorks).Error
		if err != nil {
			return nil, err
		}
	}
	// 转换 model 为 DTO
	var dtoWorks []*dto.UserWorks
	for _, work := range modelWorks {
		dtoWorks = append(dtoWorks, ModelUserWorksToDTO(&work))
	}

	return dtoWorks, nil
}

func (d *userWorkRepo) GetOne(_ context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) (*dto.UserWorks, error) {
	var modelWork model.UserWorks
	query := d.buildUserWorkQuery(d.getDb(tx), userWork).Preload("User")
	err := query.First(&modelWork).Error
	if err != nil {
		return nil, err
	}

	// 转换 model 为 DTO
	return ModelUserWorksToDTO(&modelWork), nil
}

func (d *userWorkRepo) Delete(_ context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error {
	// 假删除：设置 is_deleted = 1 和 deleted_at = 当前时间
	updates := map[string]interface{}{
		"is_deleted": 1,
		"deleted_at": "NOW()",
	}
	return d.getDb(tx).Model(&model.UserWorks{}).Where("id = ?", userWork.ID).Updates(updates).Error
}

func (d *userWorkRepo) Update(_ context.Context, id uint64, updates map[string]any, tx ...*gorm.DB) error {
	return d.getDb(tx).Model(&model.UserWorks{}).Where("id = ?", id).Updates(updates).Error
}

func (d *userWorkRepo) Count(_ context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) (int64, error) {
	var count int64
	query := d.buildUserWorkQuery(d.getDb(tx), userWork)
	err := query.Count(&count).Error
	return count, err
}
