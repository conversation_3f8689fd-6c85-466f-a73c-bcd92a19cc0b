package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"

	"github.com/gin-gonic/gin"
)

type UserInfoController struct {
	userInfoBiz *biz.UserInfoBiz
	userService *service.UserService
	log         *logger.Logger
}

func NewUserInfoController(
	bootstrap *component.BootStrap,
	userService *service.UserService,
	userInfoBiz *biz.UserInfoBiz,
) *UserInfoController {
	return &UserInfoController{
		log:         bootstrap.Log,
		userService: userService,
		userInfoBiz: userInfoBiz,
	}
}

// GetUserInfo 获取用户信息
func (c *UserInfoController) GetUserInfo(ctx *gin.Context) {
	userId := utils.GetUserId(ctx)
	if userId < 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(response.UserIdError), response.WithSLSLog)
		return
	}

	// 调用服务获取用户信息
	userInfo, err := c.userInfoBiz.GetUserInfo(userId)
	if err != nil {
		response.Response(ctx, nil, nil, err, response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, userInfo, nil, response.WithSLSLog)
}

// CancelUser 用户注销
func (c *UserInfoController) CancelUser(ctx *gin.Context) {
	userId := utils.GetUserId(ctx)
	if userId < 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(response.UserIdError), response.WithSLSLog)
		return
	}

	// 调用服务注销用户
	err := c.userService.UpdateUser(&dto.UpdateUserRequest{
		ID:       userId,
		IsDelete: int8(model.StatusEnabled),
	}, nil)

	response.Response(ctx, nil, nil, err, response.WithSLSLog)
}

// GetUserDiamondRecord 获取用户钻石记录
func (c *UserInfoController) GetUserDiamondRecord(ctx *gin.Context) {
	userId := utils.GetUserId(ctx)
	if userId < 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(response.UserIdError), response.WithSLSLog)
		return
	}

	// 调用服务获取用户钻石记录
	diamondRecords, err := c.userInfoBiz.GetUserDiamondRecord(userId)
	if err != nil {
		response.Response(ctx, nil, nil, err, response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, diamondRecords, nil, response.WithSLSLog)
}
