package service

import (
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/qiniu"
	"chongli/pkg/response"
	"chongli/pkg/stringchar"
	"fmt"
	"path"
	"time"

	"github.com/gin-gonic/gin"
)

type CommonService struct {
}

func NewCommonService() *CommonService {
	return &CommonService{}
}

// PicUpload 文件上传
func (cs *CommonService) PicUpload(ctx *gin.Context, req *dto.UploadMultiRequestDto) (resp []string, err errpkg.IError) {
	//var _err error
	_, exists := ctx.Get("user_id")
	if !exists {
		return []string{}, errpkg.NewLowError(response.PermissionDeniedError)
	}
	for i := range req.Files {
		fileName := stringchar.Md5Hex([]byte(req.Files[i].Filename), 16)
		filePath := fmt.Sprintf("%s%s%s", time.Now().Format("2006/01/02/"), fileName, path.Ext(req.Files[i].Filename))
		// ImgName, _err := qiniu.UploadFile(0, req.File, filePath)
		url, err := qiniu.UploadFile(0, req.Files[i], filePath)
		if err != nil {
			logger.Log().Error("上传文件失败: %v", err)
			return []string{}, errpkg.NewLowError(response.UploadFileError)
		}
		//// 通过文件后缀判断是否为图片类型
		//ext := strings.ToLower(path.Ext(req.Files[i].Filename))
		//// 常见图片格式后缀
		//imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
		//
		//isImage := slices.Contains(imageExts, ext)
		//
		//if isImage {
		//	errMsg := ""
		//	// 检查图片是否违规
		//	if errMsg, _err = utils.CheckImage(url); _err != nil {
		//		logger.Log().Error("CheckImage error: %v", _err)
		//		return []string{}, errpkg.NewLowError(response.UploadFileError)
		//	}
		//	if errMsg != "" {
		//		return []string{}, errpkg.NewLowError(errMsg)
		//	}
		//} else {
		//	logger.Log().Error("上传文件不是图片,%v", req.Files[i].Filename)
		//}
		resp = append(resp, url)
	}

	return resp, nil
}
