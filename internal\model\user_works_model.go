package model

import "time"

// UserWorks 用户作品
type UserWorks struct {
	ID         uint64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	UserID     uint64     `gorm:"column:user_id;NOT NULL" json:"user_id"`
	WorkType   string     `gorm:"column:work_type;NOT NULL" json:"work_type"`
	Cover      string     `gorm:"column:cover;NOT NULL" json:"cover"`
	TemplateID uint64     `gorm:"column:template_id;NOT NULL" json:"template_id"`
	Status     int8       `gorm:"column:status;default:0;NOT NULL" json:"status"`
	ErrMsg     string     `gorm:"column:error_msg;type:varchar(1024);default:null" json:"error_msg"`
	CreateAt   time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"`
	UpdatedAt  time.Time  `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL;autoUpdateTime" json:"update_at"`
	DeletedAt           *time.Time `gorm:"column:deleted_at;default:null" json:"deleted_at"`
	IsDeleted           int8       `gorm:"column:is_deleted;default:0;NOT NULL" json:"is_deleted"`
	ExpectedFinishTime  *time.Time `gorm:"column:expected_finish_time;default:null" json:"expected_finish_time"`

	Template AITemplate `gorm:"foreignKey:TemplateID;references:ID"`
	PicURL   string     `gorm:"column:pic_url;NOT NULL" json:"pic_url"`
	VideoURL string     `gorm:"column:video_url;NOT NULL" json:"video_url"`
	Diamond  int64      `gorm:"column:diamond;NOT NULL" json:"diamond"`
	User     User       `gorm:"foreignKey:UserID;references:ID"`
}

// TableName -
func (u *UserWorks) TableName() string {
	return "user_works"
}
