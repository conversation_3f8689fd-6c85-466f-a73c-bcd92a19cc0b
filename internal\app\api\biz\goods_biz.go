package biz

import (
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/utils"
)

type GoodsBiz struct {
	GuiyinService *service.GuiyinService
	goodsRepo     repo.GoodsVipRepo
}

type Goods struct {
	utils.RequestHeaderDto
}

func NewGoodsBiz(gu *service.GuiyinService, goodsRepo repo.GoodsVipRepo) *GoodsBiz {
	return &GoodsBiz{
		GuiyinService: gu,
		goodsRepo:     goodsRepo,
	}
}

func (g *GoodsBiz) GoodsList(req *Goods) ([]*dto.GoodsVipChannelDto, error) {
	channelId, err := g.GuiyinService.GetChannelIDByDeviceID(req.DeviceId, req.Channel)
	if err != nil {
		return nil, err
	}
	// 默认给个1
	if channelId == 0 {
		channelId = 1
	}
	goodsChannel, err := utils.ClearPlatform(req.Channel)
	if err != nil {
		return nil, err
	}
	goodsList, err := g.goodsRepo.ListChannelGoods(dto.GoodsVipChannelReq{
		ChannelID:        channelId,
		IsDisplay:        model.StatusEnabled,
		GoodsIsDelete:    model.StatusDisabled,
		ChannelIsDeleted: model.StatusDisabled,
		OrderBy:          "goods_vip.sort desc",
		GoodsChannel:     goodsChannel,
		Version:          req.Version,
	})
	if err != nil {
		return nil, err
	}
	return goodsList, nil
}
