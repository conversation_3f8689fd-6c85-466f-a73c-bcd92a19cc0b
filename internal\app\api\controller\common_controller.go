package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type CommonController struct {
	log              *logger.Logger
	configBiz        *biz.ConfigBiz
	guiyinApiService *biz.GuiyinApiService
	popupBiz         *biz.PopupBiz
	getuiBiz         *biz.GeTuiBiz
	bannerBiz        *biz.BannerBiz
	commonService    *service.CommonService
}

func NewCommonController(
	bootStrap *component.BootStrap,
	configBiz *biz.ConfigBiz,
	guiyinApiService *biz.GuiyinApiService,
	popupBiz *biz.PopupBiz,
	getuiBiz *biz.GeTuiBiz,
	bannerBiz *biz.BannerBiz,
	commonService *service.CommonService,
) *CommonController {
	return &CommonController{
		log:              bootStrap.Log,
		configBiz:        configBiz,
		guiyinApiService: guiyinApiService,
		popupBiz:         popupBiz,
		getuiBiz:         getuiBiz,
		bannerBiz:        bannerBiz,
		commonService:    commonService,
	}
}

// ConfigList 获取配置列表
func (c *CommonController) ConfigList(ctx *gin.Context) {
	var req biz.ConfigListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("请求参数错误: "+err.Error()), response.WithSLSLog)
		return
	}
	header := utils.RequestHeader(ctx)
	req.RequestHeaderDto = header
	if len(req.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}
	// 调用业务逻辑层
	responseData, bizErr := c.configBiz.ConfigList(&req)
	if bizErr != nil {
		c.log.Error("获取配置列表失败: %v", bizErr.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取列表失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, req, responseData, nil, response.WithSLSLog)

}

// GetVersion 版本检查更新
func (c *CommonController) GetVersion(ctx *gin.Context) {
	header := utils.RequestHeader(ctx)
	channel, err := utils.ClearPlatform(header.Channel)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("请求参数错误: "+err.Error()), response.WithSLSLog)
		return
	}

	data, err := c.configBiz.GetVersion(channel)
	if err != nil {
		c.log.Error("获取版本失败: %v", err.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取版本失败"), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, data, nil, response.WithSLSLog)
}

// BindAttributeData 绑定归因数据
func (c *CommonController) BindAttributeData(ctx *gin.Context) {

	rawData, err := ctx.GetRawData()
	if err != nil {
		ctx.String(http.StatusBadRequest, "读取失败: %v", err)
		return
	}

	if len(ctx.Request.Header.Get("deviceId")) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}
	bindDto := biz.DeviceBindInfo{
		DeviceID: ctx.Request.Header.Get("deviceId"),
		AllData:  string(rawData),
		Channel:  ctx.Request.Header.Get("channel"),
	}
	if bindDto.DeviceID == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID不能为空"), response.WithSLSLog)
		return
	}
	if bindDto.Channel == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("Channel不能为空"), response.WithSLSLog)
		return
	}
	data, errFromService := c.guiyinApiService.BindAttributeData(ctx, &bindDto)
	if errFromService != nil {
		c.log.Error("BindAttributeData error: %v", errFromService)
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("绑定归因数据失败"), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, map[string]any{"channel_id": data}, nil, response.WithSLSLog)
}

func (c *CommonController) Popup(ctx *gin.Context) {
	header := utils.RequestHeader(ctx)

	if len(header.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}
	if len(header.Channel) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("Channel是必须的"), response.WithSLSLog)
		return
	}
	req := biz.PopupListRequest{
		RequestHeaderDto: *header,
	}
	list, err := c.popupBiz.GetPopupList(ctx, &req)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取弹窗列表失败"), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, list, nil, response.WithSLSLog)

}

func (c *CommonController) BindGeTui(ctx *gin.Context) {
	var req dto.BindGeTuiCidRequest
	if errBind := ctx.ShouldBind(&req); errBind != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(response.BadRequest), response.WithSLSLog)
		return
	}
	userID := utils.GetUserId(ctx)
	if userID <= 0 {
		logger.Log().Error("获取用户id失败")
		response.Response(ctx, nil, nil, errpkg.NewMiddleError(response.UserIdError), response.WithSLSLog)
		return
	}
	req.UserId = userID
	data, err := c.getuiBiz.BindGeTui(&req)
	response.Response(ctx, req, data, err, response.WithSLSLog)
}

// GetIndexBanner 获取首页banner
func (c *CommonController) GetIndexBanner(ctx *gin.Context) {
	// 从请求头中获取版本号
	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("版本号不能为空"), response.WithSLSLog)
		return
	}

	// 调用业务逻辑层
	banners, bizErr := c.bannerBiz.GetIndexBannerList(version)
	if bizErr != nil {
		c.log.Error("获取首页banner失败: %v", bizErr.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取banner列表失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, banners, nil, response.WithSLSLog)
}

// Upload 文件上传
func (c *CommonController) Upload(ctx *gin.Context) {
	var request dto.UploadMultiRequestDto
	if errBind := ctx.ShouldBind(&request); errBind != nil {
		c.log.Error("绑定参数失败: %v", errBind.Error())
		response.Response(ctx, request, nil, errpkg.NewMiddleError("绑定参数失败"), response.WithSLSLog)
		return
	}
	// 如果长传的文件大小大于10M则返回错误
	for _, file := range request.Files {
		if file.Size >= 15*1024*1024 {
			response.Response(ctx, request, nil, errpkg.NewLowError("上传文件大小大于15MB"), response.WithSLSLog)
			return
		}
	}
	urls, err := c.commonService.PicUpload(ctx, &request)

	// 为 url 添加 https://
	for i, url := range urls {
		urls[i] = "https://" + url
	}

	response.Response(ctx, request, urls, err, response.WithSLSLog)
}
