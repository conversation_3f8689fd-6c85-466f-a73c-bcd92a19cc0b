package service

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"fmt"

	"gorm.io/gorm"
)

type UserWorkService struct {
	bootstrap    *component.BootStrap
	userWorkRepo repo.UserWorkRepo
	log          *logger.Logger
	userService  *UserService
}

func NewUserWorkService(
	bootstrap *component.BootStrap,
	userWorkRepo repo.UserWorkRepo,
	userService *UserService,
) *UserWorkService {
	return &UserWorkService{
		bootstrap:    bootstrap,
		userWorkRepo: userWorkRepo,
		log:          bootstrap.Log,
		userService:  userService,
	}
}

func (s *UserWorkService) DoneUserWork(ctx context.Context, id uint64, updates map[string]any, tx ...*gorm.DB) error {
	updates["status"] = model.StatusEnabled
	// updates["finished_at"] = time.Now()
	return s.userWorkRepo.Update(ctx, id, updates, tx...)
}

// updateUserWorkOnFailure 当任务失败时更新用户作品状态,并且返还钻石
func (s *UserWorkService) UpdateUserWorkOnFailure(ctx context.Context, workID uint64, errorMsg string, tx ...*gorm.DB) error {
	if workID <= 0 {
		fmt.Printf("WorkID <= 0，跳过用户作品更新")
		return nil
	}

	// 查找对应的用户作品
	userWork, err := s.userWorkRepo.GetOne(ctx, &dto.UserWorks{
		ID: workID,
	}, tx...)

	if err != nil {
		// 记录错误但不阻断主流程
		fmt.Printf("查找用户作品失败: %v\n", err)
		return nil // 不返回错误，避免阻断主流程
	}

	updates := map[string]any{
		"status":    model.StatusDisabled, // 设置为失败状态
		"error_msg": errorMsg,
	}

	if err := s.userWorkRepo.Update(ctx, userWork.ID, updates, tx...); err != nil {
		// 记录错误但不阻断主流程
		fmt.Printf("更新用户作品状态失败: %v\n", err)
	}

	// 返还钻石
	if userWork.Diamond > 0 {
		// 获取事务对象
		var dbTx *gorm.DB
		if len(tx) > 0 && tx[0] != nil {
			dbTx = tx[0]
		} else {
			// 如果没有传入事务，记录错误但不阻断主流程
			fmt.Printf("没有事务对象，跳过钻石返还")
			return nil
		}

		// 调用返还钻石的方法
		if err := s.userService.AddOrSubUserDiamondAndAddRecord(DiamondOperationRequest{
			UserID:  int64(userWork.UserID),
			OrderID: fmt.Sprintf("refund_%d", userWork.ID), // 使用作品ID作为订单ID
			GoodsID: "work_refund",                         // 固定商品ID表示作品退款
			Diamond: uint64(userWork.Diamond),
			Mark:    fmt.Sprintf("作品失败返还钻石，作品ID: %d", userWork.ID),
			Version: "",                       // 可以为空
			Channel: "",                       // 可以为空
			Op:      int(model.StatusEnabled), // 1表示增加钻石
		}, dbTx); err != nil {
			// 记录错误但不阻断主流程
			fmt.Printf("返还钻石失败: %v\n", err.Error())
		} else {
			fmt.Printf("成功返还钻石 %d 给用户 %d\n", userWork.Diamond, userWork.UserID)
		}
	}

	return nil
}
